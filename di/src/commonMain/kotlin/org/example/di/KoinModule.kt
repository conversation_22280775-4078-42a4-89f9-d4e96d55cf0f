package org.example.di

import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.serialization.json.Json
import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.AddReceiptViewModel
import org.example.addmultiple.presentation.AddMultipleViewModel
import org.example.csvconfig.CsvConfigViewModel

import org.example.addReceipt.mapper.OpenAiResponseMapper


import org.example.addReceipt.ScanStateReducer
import org.example.addReceipt.mapper.FormDataToReceiptMapper
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.category.CategoriesViewModel
import org.example.core.domain.OpenAIService
import org.example.core.domain.repository.AIRepository

import org.example.data.ReceiptRepositoryImpl
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.domain.repository.BudgetRepository
import org.example.data.BudgetRepositoryImpl
import org.example.core.domain.usecase.ai.ParseReceiptUseCase
import org.example.core.domain.usecase.receipt.SaveReceiptUseCase
import org.example.core.domain.usecase.receipt.UpdateReceiptUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.core.domain.usecase.product.GetProductsUseCase
import org.example.core.domain.usecase.receipt.DeleteReceiptUseCase
import org.example.core.domain.usecase.budget.GetBudgetForWeekUseCase
import org.example.core.domain.usecase.budget.SetBudgetForWeekUseCase
import org.example.core.domain.usecase.budget.UpdateBudgetUseCase
import org.example.core.domain.usecase.budget.DeleteBudgetUseCase
import org.example.core.domain.usecase.budget.GetBudgetHistoryUseCase
import org.example.dashboard.DashboardViewModel
import org.example.budget.BudgetViewModel
import org.example.core.domain.usecase.receipt.LoadReceiptUseCase
import org.example.addReceipt.mapper.ReceiptToFormDataMapper
import org.example.data.remote.AIRepositoryImpl
import org.example.data.remote.service.OpenAIRequestBuilder
import org.example.data.remote.service.OpenAIServiceImpl
import org.example.receiptlist.ReceiptListViewModel
import org.koin.core.KoinApplication
import org.koin.core.context.startKoin
import org.koin.core.module.dsl.viewModel
import org.koin.core.qualifier.named
import org.koin.dsl.module

// Default HttpClient for other services - lighter configuration
val defaultHttpClient = HttpClient(CIO) {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        requestTimeoutMillis = 30_000  // Standard timeout
        connectTimeoutMillis = 15_000
        socketTimeoutMillis = 30_000
    }
}

// Named HttpClient for OpenAI - specific configuration
val openAIHttpClient = HttpClient(CIO) {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        requestTimeoutMillis = 60_000  // OpenAI needs longer timeout
        connectTimeoutMillis = 30_000
        socketTimeoutMillis = 60_000
    }
}

val sharedModule = module {
    single<ReceiptRepository> { ReceiptRepositoryImpl(get()) }
    single<BudgetRepository> { BudgetRepositoryImpl(get()) }

    single<OpenAIService> { OpenAIServiceImpl(get(qualifier = named("openai"))) }
    single<AIRepository> { AIRepositoryImpl(get(), get()) }
    single { OpenAIRequestBuilder() }


    single(named("openai")) { openAIHttpClient }
    single { defaultHttpClient }

    // Use cases
    factory { GetTypesAndCategoriesUseCase(get()) }
    single { ParseReceiptUseCase(get(), get()) } // ino dlaczego claude zrobil to jako single?
    factory { SaveReceiptUseCase(get(), get()) }
    factory { UpdateReceiptUseCase(get(), get()) }

    factory { ReceiptToFormDataMapper() }
    factory { LoadReceiptUseCase(get()) }

    factory { DeleteReceiptUseCase(get()) }
    factory { GetReceiptsUseCase(get()) }
    factory { GetProductsUseCase(get()) }

    // Budget use cases
    factory { GetBudgetForWeekUseCase(get()) }
    factory { SetBudgetForWeekUseCase(get()) }
    factory { UpdateBudgetUseCase(get()) }
    factory { DeleteBudgetUseCase(get()) }
    factory { GetBudgetHistoryUseCase(get()) }


    single { FormDataToReceiptMapper() }
    single { OpenAiResponseMapper() }


    viewModel {
        DashboardViewModel(
            getReceiptsUseCase = get(),
            getTypesAndCategoriesUseCase = get(),
            getBudgetForWeekUseCase = get()
        )
    }

    viewModel {
        val uiState = MutableStateFlow(AddReceiptUiState())

        AddReceiptViewModel(
            _uiState = uiState,
            typesCategoriesUseCase = get(),
            scanStateReducer = ScanStateReducer(
                updateState = { transform -> uiState.update(transform) }
            ),
            saveReceiptUseCase = get(),
            updateReceiptUseCase = get(),
            loadReceiptUseCase = get(),
            receiptToFormDataMapper = get(),
            deleteReceiptUseCase = get(),
            formDataToReceiptMapper = get(),
            parseReceiptUseCase = get(),
            openAiResponseMapper = get()
        )
    }

    viewModel {
        CategoriesViewModel(
            repository = get()
        )
    }

    viewModel {
        ReceiptListViewModel(
            getReceiptsUseCase = get(),
        )
    }

    viewModel {
        BudgetViewModel(
            getBudgetForWeekUseCase = get(),
            setBudgetForWeekUseCase = get(),
            updateBudgetUseCase = get(),
            deleteBudgetUseCase = get(),
            getBudgetHistoryUseCase = get()
        )
    }

    viewModel {
        AddMultipleViewModel()
    }

    viewModel {
        CsvConfigViewModel()
    }
}


/** We will use this config parameter only on the Android target, so that we can actually pass the Android context.
So Android context might be needed in some of the future, uh, use cases where we need to inject this actual Android context.
So before we can inject this context Android context from this common mean, we need to provide it from our Android main source set.*/
fun initializeKoin(
    config: (KoinApplication.() -> Unit)? = null
) {
    startKoin {
        config?.invoke(this)
        modules(sharedModule)
    }
}