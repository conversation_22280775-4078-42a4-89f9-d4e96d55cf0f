# Tree 

zapisywanie do pliku całego projektu ScanReceipt -I ignoruje 'te foldery |oddzielone "|" '

```bash
tree -I '.gradle|.idea|build|composeApp|misc-ScanReceipt|iosApp|iosMain|gradle|*.iml|.git|BACKLOG.md|CHANGELOG.md|README.md|README_DEV.md|README_DEV_ALTERNATIVE.md|fix_migration_errors.sh

settings.gradle.kts|gradlew|gradlew.bat|local.properties|build.gradle.kts|gradle.properties|gradle|gradle-wrapper.jar|gradle-wrapper.properties|gradle-wrapper.jar|gradle-wrapper.properties|gradle-wrapper.jar|gradle-wrapper.properties|gradle-wrapper.jar|gradle-wrapper.properties|gradle-wrapper.jar|gradle-wrapper.properties|gradle-wrapper' /Users/<USER>/dev/kotlin/ScanReceipt > project-structure.txt
```
wyświetlanie konkretnego katalogu
```bash
tree /Users/<USER>/dev/kotlin/ScanReceipt/feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/

```

# Bash comands

##### Zabicie procesu
```bash
~/Library/Android/sdk/platform-tools/adb shell am kill org.example.scanreceipt
```

##### Kompilacja aplikacji
```bash
$ ./gradlew :composeApp:assembleDebug
```

##### Kompilacja modulu
```bash
$ ./gradlew :feature:addreceipt:compileDebugKotlinAndroid
```
##### Odpalenie testów
```bash
$ ./gradlew :feature:addreceipt:testDebugUnitTest --tests "*ReducerTest"
```


# Git Workflow (Model B)

## 🔹 Ogólna zasada

- **`main`** = stabilna gałąź – zawsze działa, zawiera tylko sprawdzone funkcjonalności
- **`feature branches`** = krótkie, konkretne – każda nowa funkcja/poprawka powstaje w osobnym branchu od main
- **merge do main** – po zakończeniu pracy i przetestowaniu

## 🔹 Workflow krok po kroku

### 1. Upewnij się, że masz aktualnego main

```bash
git checkout main
git pull origin main
```

### 2. Utwórz nową gałąź dla konkretnej funkcjonalności

#### Nazewnictwo:
- `feat-*` → nowa funkcjonalność
- `fix-*` → poprawka błędu
- `impr-*` → ulepszenie istniejącego kodu

#### Przykłady:
```bash
git checkout -b feat-dashboard-integrate-budget
git checkout -b feat-dashboard-new-widget
git checkout -b fix-dashboard-crash-on-empty-data
```

### 3. Rób commity w trakcie pracy

Commituj logiczne kroki (np. „add repository for budget" zamiast „fix").

Jeśli musisz odłożyć coś na bok → użyj **stash**:

```bash
git stash push -m "WIP: opis zmian"
git stash list
git stash pop stash@{0}
```

### 4. Regularnie aktualizuj swoją gałąź

Jeśli na main pojawiły się nowe rzeczy, dociągnij je:

```bash
git checkout main
git pull origin main
git checkout feat-dashboard-integrate-budget
git merge main
```

> 💡 Możesz też używać `git rebase main`, ale merge jest prostszy.

### 5. Testuj i kończ pracę

- Zrób finalny commit z opisem zmian
- Sprawdź status:

```bash
git status
```

### 6. Scal do main

```bash
git checkout main
git pull origin main
git merge feat-dashboard-integrate-budget
```

### 7. Wypchnij zmiany

```bash
git push origin main
```

### 8. Sprzątnij stary branch (opcjonalnie)

Po scaleniu można go usunąć lokalnie i zdalnie:

```bash
git branch -d feat-dashboard-integrate-budget
git push origin --delete feat-dashboard-integrate-budget
```

## 🔹 Kiedy merge, a kiedy git checkout -B?

- **`git merge main`** – używaj, gdy branch jeszcze żyje (np. robisz w nim kilka kroków i chcesz dogonić main)
- **`git checkout -B <branch> main`** – używaj, gdy chcesz zresetować branch i zacząć od czystego main (np. stary branch jest niepotrzebny albo już scaliłeś wszystko)

## 🔹 Najczęstsze sytuacje

### Nowa funkcjonalność do dashboardu

```bash
git checkout main
git pull origin main
git checkout -b feat-dashboard-new-widget
```
→ po pracy merge do main.

### Przenosisz część kodu z innego brancha

```bash
git stash push -m "WIP: opis"
git checkout main
git checkout -b feat-dashboard-integrate-budget
git stash pop
```
→ kontynuujesz w nowym branchu.

### Chcesz pozbyć się starego brancha i zacząć czysto

```bash
git checkout -B feat-dashboard main
```

## ✅ Korzyści tego modelu

Ten model (krótkie, dedykowane branche) daje Ci:
- czystą historię
- łatwiejsze ogarnianie zmian
- zero „długowiecznych potworów" w historii Git

---

## 📊 Diagramy workflow

### Model B - Ogólny przepływ

```
(main) ──●──────────●──────────●──────────●──────────●──────────●─────▶
          ↘          ↘          ↘
           ↘          ↘          ↘
        (feat-budget) (feat-dashboard) (feat-dashboard-new-widget)
         ●──●──●──●─── ●──●──●────── ●──●──●────
                 ↗         ↗           ↗
                ↗         ↗           ↗
        (merge → main) (merge → main) (merge → main)
```

**Legenda:**
- `●` = commit
- Strzałki poziome → rozwój gałęzi w czasie
- Odnogi z main → nowe feature branche
- Strzałki w górę = merge z powrotem do main po zakończeniu pracy

---

## 1️⃣ Diagram z faktycznej sytuacji (ze stash)

```
(main) ──●──────────●──────────●──────────●──────────●──────────●─────▶
          ↘
           ↘
        (feat-budget) ──●──●───▶
                        │ (UI commit)
                        │ (CRUD commit)
                        │
                        └─ stash "integracja dashboard"

(main) ◀─ merge feat-budget (budget w main)

(main) ──●──────────●──────────●────▶
          ↘
           ↘
        (feat-dashboard) reset → main (git checkout -B)
                        │
                        └─ stash pop "integracja dashboard"
                         ●── commit "Integracja"

(main) ◀─ merge feat-dashboard
```

### Proces krok po kroku:

1. Na `feat-budget` powstały UI + CRUD
2. Integrację z dashboardem zrobiłeś „za wcześnie", więc wrzuciłeś do stash
3. Potem zmergowałeś `feat-budget` do main
4. Zresetowałeś `feat-dashboard` na czysto z main
5. Odtworzyłeś stash na `feat-dashboard` → commit integracyjny
6. Merge do main

---

## 2️⃣ Alternatywa z cherry-pick

```
(main) ──●──────────●──────────●──────────●──────────●─────▶
          ↘
           ↘
        (feat-budget) ──●──●───▶
                        │ (UI commit)
                        │ (CRUD commit)
                        │ (Integracja commit ❌ za wcześnie)

(main) ◀─ merge feat-budget (tylko UI+CRUD)

(main) ──●──────────●─────▶
          ↘
           ↘
        (feat-dashboard) ◀─ cherry-pick "Integracja commit" z feat-budget
                         ●── commit "Integracja"

(main) ◀─ merge feat-dashboard
```

### Różnica:

- Zamiast stash → możesz po prostu wziąć commit z innej gałęzi za pomocą `git cherry-pick`
- Cherry-pick kopiuje jeden commit (albo zakres commitów) z dowolnej gałęzi i wkleja go do Twojej aktualnej
- Masz czystszą historię (commit nie znika, tylko zostaje powielony)

---

## 3️⃣ Git cherry-pick - ściąga

### Podstawowe użycie:

```bash
git checkout feat-dashboard
git cherry-pick <commit-hash>
```
→ skopiuje wskazany commit z innej gałęzi do `feat-dashboard`.

### Zakres commitów:

```bash
git cherry-pick A^..B
```
→ weź wszystkie commity od A (exclusive) do B (inclusive).

### Rozwiązywanie konfliktów:

Jeśli pojawią się konflikty, git zatrzyma cherry-pick. Wtedy:

```bash
git status              # zobacz pliki w konflikcie
git add <plik>...      # po naprawieniu
git cherry-pick --continue
```

### Przerwanie cherry-pick:

```bash
git cherry-pick --abort
```

---

## ✅ Podsumowanie

- **Stash** = szybki notatnik dla brudnych zmian, które nie pasują jeszcze do commita
- **Cherry-pick** = chirurgiczne kopiowanie commitów między gałęziami
- W przyszłości, gdy masz „za wcześnie zrobiony commit" (np. integracja z innym modułem), zamiast stash → robisz osobny commit i potem przenosisz go na odpowiedni branch cherry-pickiem
