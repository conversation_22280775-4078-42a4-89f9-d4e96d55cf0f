package org.example.csvconfig

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * ViewModel for CSV Configuration Screen
 * Follows the project's MVVM pattern and Clean Architecture principles
 */
class CsvConfigViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow(CsvConfigUiState())
    val uiState: StateFlow<CsvConfigUiState> = _uiState.asStateFlow()
    
    init {
        // Initialize with default preview
        generatePreview()
    }
    
    /**
     * Handle events from the UI
     */
    fun onEvent(event: CsvConfigEvent) {
        when (event) {
            // Settings events
            is CsvConfigEvent.ToggleIncludeHeaders -> updateIncludeHeaders(event.include)
            is CsvConfigEvent.ChangeHeaderScope -> updateHeaderScope(event.scope)
            is CsvConfigEvent.ToggleNoDuplicateData -> updateNoDuplicateData(event.noDuplicate)
            is CsvConfigEvent.ToggleAddEmptyRow -> updateAddEmptyRow(event.addEmpty)
            
            // Field configuration events
            is CsvConfigEvent.ToggleField -> toggleField(event.fieldKey)
            is CsvConfigEvent.StartEditingField -> startEditingField(event.fieldKey)
            is CsvConfigEvent.UpdateFieldLabel -> updateFieldLabel(event.fieldKey, event.newLabel)
            is CsvConfigEvent.StopEditingField -> stopEditingField()
            is CsvConfigEvent.MoveField -> moveField(event.fieldKey, event.direction)
            
            // Preview events
            is CsvConfigEvent.ChangePreviewTab -> changePreviewTab(event.tab)
            
            // Action events
            is CsvConfigEvent.SaveConfiguration -> saveConfiguration()
            is CsvConfigEvent.CancelConfiguration -> cancelConfiguration()
            is CsvConfigEvent.DismissError -> dismissError()
        }
    }
    
    private fun updateIncludeHeaders(include: Boolean) {
        _uiState.update { currentState ->
            currentState.copy(
                csvSettings = currentState.csvSettings.copy(includeHeaders = include)
            )
        }
        generatePreview()
    }
    
    private fun updateHeaderScope(scope: HeaderScope) {
        _uiState.update { currentState ->
            currentState.copy(
                csvSettings = currentState.csvSettings.copy(headerScope = scope)
            )
        }
        generatePreview()
    }
    
    private fun updateNoDuplicateData(noDuplicate: Boolean) {
        _uiState.update { currentState ->
            currentState.copy(
                csvSettings = currentState.csvSettings.copy(noDuplicateData = noDuplicate)
            )
        }
        generatePreview()
    }
    
    private fun updateAddEmptyRow(addEmpty: Boolean) {
        _uiState.update { currentState ->
            currentState.copy(
                csvSettings = currentState.csvSettings.copy(addEmptyRow = addEmpty)
            )
        }
        generatePreview()
    }
    
    private fun toggleField(fieldKey: String) {
        // Don't allow toggling disabled fields
        val field = _uiState.value.receiptFields.find { it.key == fieldKey }
        if (field?.editable == false) return
        
        _uiState.update { currentState ->
            currentState.copy(
                receiptFields = currentState.receiptFields.map { field ->
                    if (field.key == fieldKey) {
                        field.copy(enabled = !field.enabled)
                    } else field
                }
            )
        }
        generatePreview()
    }
    
    private fun startEditingField(fieldKey: String) {
        _uiState.update { currentState ->
            currentState.copy(
                ui = currentState.ui.copy(editingFieldKey = fieldKey)
            )
        }
    }
    
    private fun updateFieldLabel(fieldKey: String, newLabel: String) {
        _uiState.update { currentState ->
            currentState.copy(
                receiptFields = currentState.receiptFields.map { field ->
                    if (field.key == fieldKey) {
                        field.copy(label = newLabel)
                    } else field
                }
            )
        }
        generatePreview()
    }
    
    private fun stopEditingField() {
        _uiState.update { currentState ->
            currentState.copy(
                ui = currentState.ui.copy(editingFieldKey = null)
            )
        }
    }
    
    private fun moveField(fieldKey: String, direction: MoveDirection) {
        val currentFields = _uiState.value.receiptFields
        val currentIndex = currentFields.indexOfFirst { it.key == fieldKey }
        
        if (currentIndex == -1) return
        
        val targetIndex = when (direction) {
            MoveDirection.UP -> if (currentIndex > 0) currentIndex - 1 else return
            MoveDirection.DOWN -> if (currentIndex < currentFields.size - 1) currentIndex + 1 else return
        }
        
        val newFields = currentFields.toMutableList()
        val fieldToMove = newFields.removeAt(currentIndex)
        newFields.add(targetIndex, fieldToMove)
        
        // Update order values
        val reorderedFields = newFields.mapIndexed { index, field ->
            field.copy(order = index)
        }
        
        _uiState.update { currentState ->
            currentState.copy(receiptFields = reorderedFields)
        }
        generatePreview()
    }
    
    private fun changePreviewTab(tab: PreviewTab) {
        _uiState.update { currentState ->
            currentState.copy(
                previewData = currentState.previewData.copy(activeTab = tab)
            )
        }
    }
    
    private fun saveConfiguration() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                
                // TODO: Implement actual save logic using use cases
                // For now, just simulate success
                
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        ui = it.ui.copy(showSaveConfirmation = true)
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        errorMessage = "Failed to save configuration: ${e.message}"
                    )
                }
            }
        }
    }
    
    private fun cancelConfiguration() {
        // TODO: Navigate back or reset to saved state
    }
    
    private fun dismissError() {
        _uiState.update { currentState ->
            currentState.copy(errorMessage = null)
        }
    }
    
    /**
     * Generate preview data based on current settings
     */
    private fun generatePreview() {
        val currentState = _uiState.value
        val enabledFields = currentState.receiptFields
            .filter { it.enabled }
            .sortedBy { it.order }
        
        val csvContent = generateCsvPreview(currentState.csvSettings, enabledFields)
        val excelData = generateExcelPreview(currentState.csvSettings, enabledFields)
        
        _uiState.update { state ->
            state.copy(
                previewData = state.previewData.copy(
                    csvContent = csvContent,
                    excelData = excelData
                )
            )
        }
    }
    
    /**
     * Generate CSV preview content
     */
    private fun generateCsvPreview(settings: CsvSettings, enabledFields: List<ReceiptField>): String {
        val sampleData = getSampleData()
        val fieldLabels = enabledFields.map { it.label }
        
        val lines = mutableListOf<String>()
        
        // Add headers if enabled
        if (settings.includeHeaders) {
            lines.add(fieldLabels.joinToString(";"))
        }
        
        // Add sample data rows
        sampleData.forEachIndexed { index, product ->
            val row = enabledFields.map { field ->
                getFieldValue(field.key, product, sampleData.first(), index, settings.noDuplicateData)
            }
            lines.add(row.joinToString(";"))
        }
        
        // Add empty row if enabled
        if (settings.addEmptyRow) {
            lines.add(fieldLabels.map { "" }.joinToString(";"))
        }
        
        return lines.joinToString("\n")
    }
    
    /**
     * Generate Excel preview data
     */
    private fun generateExcelPreview(settings: CsvSettings, enabledFields: List<ReceiptField>): ExcelPreviewData {
        val sampleData = getSampleData()
        val fieldLabels = enabledFields.map { it.label }
        
        val rows = mutableListOf<List<String>>()
        
        // Add sample data rows
        sampleData.forEachIndexed { index, product ->
            val row = enabledFields.map { field ->
                getFieldValue(field.key, product, sampleData.first(), index, settings.noDuplicateData)
            }
            rows.add(row)
        }
        
        // Add empty row if enabled
        if (settings.addEmptyRow) {
            rows.add(fieldLabels.map { "" })
        }
        
        return ExcelPreviewData(
            headers = if (settings.includeHeaders) fieldLabels else emptyList(),
            rows = rows
        )
    }
    
    /**
     * Get sample data for preview
     */
    private fun getSampleData(): List<SampleProduct> = listOf(
        SampleProduct("REKLAMOWKA 35L", "1", "0.65", "0.65", "Inne", "Niezbędne"),
        SampleProduct("NAPOJ PEPSI COLA 1L", "1", "6.79", "6.79", "Słodycze", "Ograniczyć"),
        SampleProduct("CHIPSY LAY'S 180G", "1", "9.49", "9.49", "Słodycze", "Ograniczyć")
    )
    
    /**
     * Get field value for preview
     */
    private fun getFieldValue(
        fieldKey: String,
        product: SampleProduct,
        baseData: SampleProduct,
        index: Int,
        noDuplicateData: Boolean
    ): String {
        return when (fieldKey) {
            "storeName" -> if (noDuplicateData && index > 0) "" else "Biedronka"
            "storeAddress" -> if (noDuplicateData && index > 0) "" else "Ul. Błotna 65-135"
            "receiptSum" -> if (noDuplicateData && index > 0) "" else "70.17"
            "purchaseDateTime" -> if (noDuplicateData && index > 0) "" else "2025-09-19T14:51:00"
            "purchaseMethod" -> if (noDuplicateData && index > 0) "" else "INNA Karta Pt."
            "productName" -> product.name
            "quantity" -> product.quantity
            "unitPrice" -> product.unitPrice
            "totalPrice" -> product.totalPrice
            "category" -> product.category
            "type" -> product.type
            else -> ""
        }
    }
}

/**
 * Sample product data for preview
 */
private data class SampleProduct(
    val name: String,
    val quantity: String,
    val unitPrice: String,
    val totalPrice: String,
    val category: String,
    val type: String
)
