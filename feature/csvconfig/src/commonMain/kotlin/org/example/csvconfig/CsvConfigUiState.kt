package org.example.csvconfig

import androidx.compose.runtime.Stable

/**
 * UI State for CSV Configuration Screen
 * Following the project's Clean Architecture and state management patterns
 */
@Stable
data class CsvConfigUiState(
    val csvSettings: CsvSettings = CsvSettings(),
    val receiptFields: List<ReceiptField> = getDefaultReceiptFields(),
    val previewData: PreviewData = PreviewData(),
    val ui: UiState = UiState(),
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * CSV export settings configuration
 */
@Stable
data class CsvSettings(
    val includeHeaders: Boolean = true,
    val headerScope: HeaderScope = HeaderScope.FIRST,
    val noDuplicateData: Boolean = false,
    val addEmptyRow: Boolean = false
)

/**
 * Header scope options for CSV export
 */
enum class HeaderScope {
    FIRST, // Headers for first receipt only
    EACH   // Headers for each receipt
}

/**
 * Receipt field configuration for CSV export
 */
@Stable
data class ReceiptField(
    val key: String,
    val label: String,
    val enabled: Boolean,
    val editable: Boolean = true,
    val order: Int
)

/**
 * Preview data for CSV and Excel views
 */
@Stable
data class PreviewData(
    val activeTab: PreviewTab = PreviewTab.CSV,
    val csvContent: String = "",
    val excelData: ExcelPreviewData = ExcelPreviewData()
)

/**
 * Preview tab options
 */
enum class PreviewTab {
    CSV, EXCEL
}

/**
 * Excel preview data structure
 */
@Stable
data class ExcelPreviewData(
    val headers: List<String> = emptyList(),
    val rows: List<List<String>> = emptyList()
)

/**
 * UI-specific state (not data)
 */
@Stable
data class UiState(
    val editingFieldKey: String? = null,
    val showSaveConfirmation: Boolean = false
)

/**
 * Events for CSV Configuration Screen
 */
sealed class CsvConfigEvent {
    // Settings events
    data class ToggleIncludeHeaders(val include: Boolean) : CsvConfigEvent()
    data class ChangeHeaderScope(val scope: HeaderScope) : CsvConfigEvent()
    data class ToggleNoDuplicateData(val noDuplicate: Boolean) : CsvConfigEvent()
    data class ToggleAddEmptyRow(val addEmpty: Boolean) : CsvConfigEvent()
    
    // Field configuration events
    data class ToggleField(val fieldKey: String) : CsvConfigEvent()
    data class StartEditingField(val fieldKey: String) : CsvConfigEvent()
    data class UpdateFieldLabel(val fieldKey: String, val newLabel: String) : CsvConfigEvent()
    data class StopEditingField(val fieldKey: String) : CsvConfigEvent()
    data class MoveField(val fieldKey: String, val direction: MoveDirection) : CsvConfigEvent()
    
    // Preview events
    data class ChangePreviewTab(val tab: PreviewTab) : CsvConfigEvent()
    
    // Action events
    data object SaveConfiguration : CsvConfigEvent()
    data object CancelConfiguration : CsvConfigEvent()
    data object DismissError : CsvConfigEvent()
}

/**
 * Direction for moving fields
 */
enum class MoveDirection {
    UP, DOWN
}

/**
 * Default receipt fields configuration
 */
fun getDefaultReceiptFields(): List<ReceiptField> = listOf(
    ReceiptField("storeName", "Nazwa sklepu", true, true, 0),
    ReceiptField("storeAddress", "Adres sklepu", true, true, 1),
    ReceiptField("productName", "product", true, true, 2),
    ReceiptField("quantity", "quantity", true, true, 3),
    ReceiptField("unitPrice", "unitPrice", true, true, 4),
    ReceiptField("totalPrice", "totalPrice", true, true, 5),
    ReceiptField("category", "category", true, true, 6),
    ReceiptField("type", "type", true, true, 7),
    ReceiptField("purchaseDateTime", "purchaseDateTime", true, true, 8),
    ReceiptField("receiptSum", "receiptSum", true, true, 9),
    ReceiptField("purchaseMethod", "purchaseMethod", true, true, 10),
    ReceiptField("discount", "-rabat", false, false, 11),
    ReceiptField("priceAfterDiscount", "cena po rabacie", false, false, 12)
)
