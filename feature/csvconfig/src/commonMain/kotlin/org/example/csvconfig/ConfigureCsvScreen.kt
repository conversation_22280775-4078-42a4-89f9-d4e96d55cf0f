package org.example.csvconfig

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import org.example.csvconfig.components.*
import org.koin.compose.viewmodel.koinViewModel

/**
 * Main CSV Configuration Screen
 * Recreates the React component functionality in Jetpack Compose
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConfigureCsvScreen(
    navigateBack: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val viewModel = koinViewModel<CsvConfigViewModel>()
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(
                            text = "Konfiguruj .csv",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = "Konfiguracja dla wszystkich przyszłych eksportów",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                },
                navigationIcon = {
                    IconButton(onClick = navigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Navigate back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            )
        },
        bottomBar = {
            BottomActionBar(
                onCancel = { viewModel.onEvent(CsvConfigEvent.CancelConfiguration) },
                onSave = { viewModel.onEvent(CsvConfigEvent.SaveConfiguration) },
                isLoading = uiState.isLoading
            )
        },
        containerColor = MaterialTheme.colorScheme.background
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            // Configuration Options Section
            item {
                ConfigurationOptionsSection(
                    csvSettings = uiState.csvSettings,
                    onEvent = viewModel::onEvent
                )
            }

            // Preview Section
            item {
                PreviewSection(
                    previewData = uiState.previewData,
                    csvSettings = uiState.csvSettings,
                    onEvent = viewModel::onEvent
                )
            }

            // Field Configuration Section
            item {
                Text(
                    text = "Kliknij na komponent aby uwzględnić go w .csv",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            items(
                items = uiState.receiptFields,
                key = { it.key }
            ) { field ->
                FieldConfigurationItem(
                    field = field,
                    isEditing = uiState.ui.editingFieldKey == field.key,
                    onToggle = { viewModel.onEvent(CsvConfigEvent.ToggleField(field.key)) },
                    onStartEdit = { viewModel.onEvent(CsvConfigEvent.StartEditingField(field.key)) },
                    onLabelUpdate = { newLabel ->
                        viewModel.onEvent(CsvConfigEvent.UpdateFieldLabel(field.key, newLabel))
                    },
                    onStopEdit = { viewModel.onEvent(CsvConfigEvent.StopEditingField(field.key)) }
                )
            }
        }
    }

    // Error handling
    uiState.errorMessage?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // Show snackbar or error dialog
            // For now, just dismiss after showing
            viewModel.onEvent(CsvConfigEvent.DismissError)
        }
    }
}

/**
 * Configuration options section with checkboxes and radio buttons
 */
@Composable
private fun ConfigurationOptionsSection(
    csvSettings: CsvSettings,
    onEvent: (CsvConfigEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Include Headers Checkbox
        LabeledCheckbox(
            checked = csvSettings.includeHeaders,
            onCheckedChange = { onEvent(CsvConfigEvent.ToggleIncludeHeaders(it)) },
            label = "Zawrzyj headery"
        )

        // Header Scope Selection (only shown when headers are included)
        if (csvSettings.includeHeaders) {
            HeaderScopeSelector(
                selectedScope = csvSettings.headerScope,
                onScopeChanged = { onEvent(CsvConfigEvent.ChangeHeaderScope(it)) },
                modifier = Modifier.padding(start = 32.dp)
            )
        }

        // Additional Options
        LabeledCheckbox(
            checked = csvSettings.noDuplicateData,
            onCheckedChange = { onEvent(CsvConfigEvent.ToggleNoDuplicateData(it)) },
            label = "Nie powtarzaj tych samych danych"
        )

        LabeledCheckbox(
            checked = csvSettings.addEmptyRow,
            onCheckedChange = { onEvent(CsvConfigEvent.ToggleAddEmptyRow(it)) },
            label = "Dodaj pusty row na końcu"
        )
    }
}

/**
 * Preview section with tabs and content
 */
@Composable
private fun PreviewSection(
    previewData: PreviewData,
    csvSettings: CsvSettings,
    onEvent: (CsvConfigEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "Podgląd .csv",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )

        // Tab Selector
        PreviewTabSelector(
            activeTab = previewData.activeTab,
            onTabSelected = { onEvent(CsvConfigEvent.ChangePreviewTab(it)) }
        )

        // Preview Content
        when (previewData.activeTab) {
            PreviewTab.CSV -> {
                CsvPreviewContent(csvContent = previewData.csvContent)
            }
            PreviewTab.EXCEL -> {
                ExcelPreviewContent(
                    excelData = previewData.excelData,
                    includeHeaders = csvSettings.includeHeaders
                )
            }
        }
    }
}

/**
 * Bottom action bar with Cancel and Save buttons
 */
@Composable
private fun BottomActionBar(
    onCancel: () -> Unit,
    onSave: () -> Unit,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedButton(
                onClick = onCancel,
                modifier = Modifier.weight(1f),
                enabled = !isLoading
            ) {
                Text("Anuluj")
            }

            Button(
                onClick = onSave,
                modifier = Modifier.weight(1f),
                enabled = !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text("Zapisz konfigurację")
                }
            }
        }
    }
}

