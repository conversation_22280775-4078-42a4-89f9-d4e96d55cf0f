package org.example.csvconfig.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import org.example.csvconfig.*

/**
 * Checkbox with label component
 */
@Composable
fun LabeledCheckbox(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onCheckedChange(!checked) }
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = checked,
            onCheckedChange = onCheckedChange,
            enabled = enabled,
            colors = CheckboxDefaults.colors(
                checkedColor = MaterialTheme.colorScheme.primary,
                uncheckedColor = MaterialTheme.colorScheme.onSurfaceVariant
            )
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            color = if (enabled) MaterialTheme.colorScheme.onSurface 
                   else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Radio button group for header scope selection
 */
@Composable
fun HeaderScopeSelector(
    selectedScope: HeaderScope,
    onScopeChanged: (HeaderScope) -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            RadioButtonOption(
                selected = selectedScope == HeaderScope.FIRST,
                onClick = { onScopeChanged(HeaderScope.FIRST) },
                label = "Dla pierwszego paragonu",
                enabled = enabled
            )
            RadioButtonOption(
                selected = selectedScope == HeaderScope.EACH,
                onClick = { onScopeChanged(HeaderScope.EACH) },
                label = "Dla każdego paragonu",
                enabled = enabled
            )
        }
    }
}

/**
 * Individual radio button option
 */
@Composable
private fun RadioButtonOption(
    selected: Boolean,
    onClick: () -> Unit,
    label: String,
    enabled: Boolean = true
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onClick() }
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = selected,
            onClick = onClick,
            enabled = enabled,
            colors = RadioButtonDefaults.colors(
                selectedColor = MaterialTheme.colorScheme.primary,
                unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
            )
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = if (enabled) MaterialTheme.colorScheme.onSurface 
                   else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Field configuration item with toggle and edit functionality
 */
@Composable
fun FieldConfigurationItem(
    field: ReceiptField,
    isEditing: Boolean,
    onToggle: () -> Unit,
    onStartEdit: () -> Unit,
    onLabelUpdate: (String) -> Unit,
    onStopEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isDisabled = !field.editable
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = !isDisabled) {
                if (field.enabled && !isEditing) {
                    onStartEdit()
                } else if (!field.enabled) {
                    onToggle()
                }
            },
        colors = CardDefaults.cardColors(
            containerColor = when {
                isDisabled -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                field.enabled -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                else -> MaterialTheme.colorScheme.surface
            }
        ),
        border = BorderStroke(
            width = 2.dp,
            color = when {
                isDisabled -> MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                field.enabled -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
            }
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier.padding(16.dp)
        ) {
            if (isEditing) {
                EditableFieldLabel(
                    initialValue = field.label,
                    onValueChange = onLabelUpdate,
                    onDone = onStopEdit
                )
            } else {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = field.label,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = if (field.enabled && !isDisabled) FontWeight.Medium else FontWeight.Normal,
                        color = when {
                            isDisabled -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
                            field.enabled -> MaterialTheme.colorScheme.onPrimaryContainer
                            else -> MaterialTheme.colorScheme.onSurface
                        }
                    )
                    
                    if (field.enabled && !isDisabled) {
                        IconButton(
                            onClick = { onToggle() },
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Remove field",
                                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Editable text field for field labels
 */
@Composable
private fun EditableFieldLabel(
    initialValue: String,
    onValueChange: (String) -> Unit,
    onDone: () -> Unit
) {
    var textValue by remember { mutableStateOf(initialValue) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
    
    BasicTextField(
        value = textValue,
        onValueChange = { textValue = it },
        modifier = Modifier
            .fillMaxWidth()
            .focusRequester(focusRequester),
        textStyle = MaterialTheme.typography.bodyLarge.copy(
            color = MaterialTheme.colorScheme.onPrimaryContainer,
            fontWeight = FontWeight.Medium
        ),
        singleLine = true,
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
        keyboardActions = KeyboardActions(
            onDone = {
                onValueChange(textValue)
                onDone()
                keyboardController?.hide()
            }
        )
    )
}

/**
 * Preview tab selector
 */
@Composable
fun PreviewTabSelector(
    activeTab: PreviewTab,
    onTabSelected: (PreviewTab) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth()
    ) {
        PreviewTab.entries.forEach { tab ->
            val isSelected = activeTab == tab
            
            Box(
                modifier = Modifier
                    .weight(1f)
                    .clickable { onTabSelected(tab) }
                    .padding(vertical = 12.dp, horizontal = 16.dp)
                    .background(
                        color = if (isSelected) Color.Transparent else Color.Transparent,
                        shape = RoundedCornerShape(8.dp)
                    )
                    .border(
                        width = if (isSelected) 2.dp else 0.dp,
                        color = if (isSelected) MaterialTheme.colorScheme.primary else Color.Transparent,
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = when (tab) {
                        PreviewTab.CSV -> ".csv"
                        PreviewTab.EXCEL -> "excel"
                    },
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    color = if (isSelected) MaterialTheme.colorScheme.primary 
                           else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
    
    // Bottom border
    HorizontalDivider(
        modifier = Modifier.fillMaxWidth(),
        thickness = 1.dp,
        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
    )
}

/**
 * CSV preview content
 */
@Composable
fun CsvPreviewContent(
    csvContent: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
//                .heightIn(min = 120.dp, max = 240.dp)
                .wrapContentHeight()
                .padding(16.dp)
        ) {
            Text(
                text = csvContent,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Excel preview content with unified horizontal and vertical scrolling
 */
@Composable
fun ExcelPreviewContent(
    excelData: ExcelPreviewData,
    includeHeaders: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(12.dp),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 200.dp, max = 400.dp)
        ) {
            ExcelTable(
                excelData = excelData,
                includeHeaders = includeHeaders,
//                modifier = Modifier.fillMaxSize()
                modifier = Modifier.fillMaxWidth().wrapContentHeight()
            )
        }
    }
}

/**
 * Excel-like table with unified horizontal and vertical scrolling
 */
@Composable
private fun ExcelTable(
    excelData: ExcelPreviewData,
    includeHeaders: Boolean,
    modifier: Modifier = Modifier
) {
    val borderColor = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
    val headerBackgroundColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
    val cellBackgroundColor = MaterialTheme.colorScheme.surface

    // Calculate column widths based on content
    val columnWidths = remember(excelData) {
        calculateColumnWidths(excelData, includeHeaders)
    }

    // Shared scroll states for the entire table
    val horizontalScrollState = rememberScrollState()
    val verticalScrollState = rememberScrollState()

    Column(
        modifier = modifier
            .horizontalScroll(horizontalScrollState)
            .verticalScroll(verticalScrollState)
    ) {
        // Column headers row (A, B, C, etc.)
        ExcelHeaderRow(
            columnCount = excelData.headers.size,
            columnWidths = columnWidths,
            borderColor = borderColor,
            backgroundColor = headerBackgroundColor
        )

        // Data headers row (if enabled)
        if (includeHeaders && excelData.headers.isNotEmpty()) {
            ExcelDataRow(
                rowIndex = 1,
                rowData = excelData.headers,
                columnWidths = columnWidths,
                borderColor = borderColor,
                backgroundColor = headerBackgroundColor,
                isHeader = true
            )
        }

        // Data rows
        excelData.rows.forEachIndexed { index, row ->
            val displayRowIndex = if (includeHeaders) index + 2 else index + 1
            ExcelDataRow(
                rowIndex = displayRowIndex,
                rowData = row,
                columnWidths = columnWidths,
                borderColor = borderColor,
                backgroundColor = cellBackgroundColor,
                isHeader = false
            )
        }
    }
}

/**
 * Calculate optimal column widths based on content
 */
private fun calculateColumnWidths(
    excelData: ExcelPreviewData,
    includeHeaders: Boolean
): List<Int> {
    val minWidth = 80 // Minimum column width in dp
    val maxWidth = 200 // Maximum column width in dp

    return excelData.headers.mapIndexed { columnIndex, header ->
        val headerLength = if (includeHeaders) header.length else 0
        val maxDataLength = excelData.rows.maxOfOrNull { row ->
            row.getOrNull(columnIndex)?.length ?: 0
        } ?: 0

        val contentBasedWidth = maxOf(headerLength, maxDataLength) * 8 + 16 // 8dp per character + padding
        contentBasedWidth.coerceIn(minWidth, maxWidth)
    }
}

/**
 * Excel column header row (A, B, C, etc.)
 */
@Composable
private fun ExcelHeaderRow(
    columnCount: Int,
    columnWidths: List<Int>,
    borderColor: Color,
    backgroundColor: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
    ) {
        // Row number header cell (empty)
        ExcelCell(
            content = "",
            width = 40.dp,
            height = 32.dp,
            borderColor = borderColor,
            backgroundColor = backgroundColor,
            textStyle = MaterialTheme.typography.labelSmall,
            textColor = MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = FontWeight.Bold
        )

        // Column headers (A, B, C, etc.)
        repeat(columnCount) { index ->
            val columnLetter = ('A' + index).toString()
            ExcelCell(
                content = columnLetter,
                width = columnWidths.getOrElse(index) { 80 }.dp,
                height = 32.dp,
                borderColor = borderColor,
                backgroundColor = backgroundColor,
                textStyle = MaterialTheme.typography.labelSmall,
                textColor = MaterialTheme.colorScheme.onSurfaceVariant,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Excel data row with row number and cells
 */
@Composable
private fun ExcelDataRow(
    rowIndex: Int,
    rowData: List<String>,
    columnWidths: List<Int>,
    borderColor: Color,
    backgroundColor: Color,
    isHeader: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
    ) {
        // Row number cell
        ExcelCell(
            content = rowIndex.toString(),
            width = 40.dp,
            height = 36.dp,
            borderColor = borderColor,
            backgroundColor = if (isHeader) backgroundColor else MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f),
            textStyle = MaterialTheme.typography.bodySmall,
            textColor = MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = if (isHeader) FontWeight.Bold else FontWeight.Normal,
            textAlign = TextAlign.Center
        )

        // Data cells
        rowData.forEachIndexed { cellIndex, cellData ->
            ExcelCell(
                content = cellData,
                width = columnWidths.getOrElse(cellIndex) { 80 }.dp,
                height = 36.dp,
                borderColor = borderColor,
                backgroundColor = backgroundColor,
                textStyle = MaterialTheme.typography.bodySmall,
                textColor = if (cellData.isEmpty()) {
                    MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                } else {
                    MaterialTheme.colorScheme.onSurface
                },
                fontWeight = if (isHeader) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

/**
 * Individual Excel cell with border and content
 */
@Composable
private fun ExcelCell(
    content: String,
    width: Dp,
    height: Dp,
    borderColor: Color,
    backgroundColor: Color,
    textStyle: TextStyle,
    textColor: Color,
    fontWeight: FontWeight = FontWeight.Normal,
    textAlign: TextAlign = TextAlign.Start,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .width(width)
            .height(height)
            .background(backgroundColor)
            .border(
                width = 0.5.dp,
                color = borderColor
            ),
        contentAlignment = when (textAlign) {
            TextAlign.Center -> Alignment.Center
            TextAlign.End -> Alignment.CenterEnd
            else -> Alignment.CenterStart
        }
    ) {
        Text(
            text = content,
            style = textStyle,
            color = textColor,
            fontWeight = fontWeight,
            textAlign = textAlign,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}
