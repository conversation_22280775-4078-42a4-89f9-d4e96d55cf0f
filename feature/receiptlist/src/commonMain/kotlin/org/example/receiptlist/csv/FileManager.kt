package org.example.receiptlist.csv

import kotlinx.coroutines.runBlocking
//import org.example.addReceipt.formatPrice
import org.example.core.domain.model.Receipt
import org.example.core.domain.service.ImageFileManager
import org.example.data.CategoriesTypesRoomImpl
import org.example.data.database.AppDatabase
import org.example.data.database.DatabaseInitializer
import org.koin.dsl.module

interface FileManager {
     suspend fun shareFile(csvContent: String, fileName: String)
     fun generateCsv(receipt: Receipt): String
}

/** from FileManager.ios.kt /Users/<USER>/dev/kotlin/ScanReceipt/feature/addreceipt/src/iosMain/kotlin/org/example/addReceipt/csv/FileManager.ios.kt */
//class IosFileManager : FileManager {
//
//     override suspend fun shareFile(csvContent: String, fileName: String) {
//          TODO("Not yet implemented")
//     }
//
//     override fun generateCsv(receipt: Receipt): String {
//          TODO("Not yet implemented")
//     }
//}

/** from /Users/<USER>/dev/kotlin/ScanReceipt/di/src/androidMain/kotlin/org/example/di/AndroidModule.kt*/
//val androidModule = module {
//     single<FileManager>{AndroidFileManager(androidContext())}
//     single<ImageFileManager> { ReceiptImageManager(androidContext()) }
//
//     // Room database dependencies
//     single<AppDatabase> {
//          val database = createDatabase(androidContext())
//          // Initialize database with default data
//          runBlocking {
//               DatabaseInitializer.initializeDatabase(database)
//          }
//          database
//     }
//
//     // Room-based repository implementation
//     single<org.example.core.domain.repository.CategoriesTypesRepository> {
//          CategoriesTypesRoomImpl(get())
//     }
//}

/** from /Users/<USER>/dev/kotlin/ScanReceipt/feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/csv/FileManager.android.kt*/

//val androidModule = module {
//     single<FileManager>{AndroidFileManager(androidContext())}
//     single<ImageFileManager> { ReceiptImageManager(androidContext()) }
//
//     // Room database dependencies
//     single<AppDatabase> {
//          val database = createDatabase(androidContext())
//          // Initialize database with default data
//          runBlocking {
//               DatabaseInitializer.initializeDatabase(database)
//          }
//          database
//     }
//
//     // Room-based repository implementation
//     single<org.example.core.domain.repository.CategoriesTypesRepository> {
//          CategoriesTypesRoomImpl(get())
//     }
//}

/** from /Users/<USER>/dev/kotlin/ScanReceipt/feature/addreceipt/src/androidMain/kotlin/org/example/addReceipt/csv/FileManager.android.kt*/
//class AndroidFileManager(private val context: Context) : FileManager {
//
//     override suspend fun shareFile(csvContent: String, fileName: String) {
//          val file = File(context.cacheDir, fileName)
//          file.writeText(csvContent)
//
//          val uri = FileProvider.getUriForFile(
//               context,
//               "${context.packageName}.fileprovider",
//               file
//          )
//
//          val shareIntent = Intent(Intent.ACTION_SEND).apply {
//               type = "text/csv"
//               putExtra(Intent.EXTRA_STREAM, uri)
//               addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//          }
//
//
//
//          context.startActivity(
//               Intent.createChooser(shareIntent, "Share CSV")
//                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//          )
//     }
//
//     override fun generateCsv(receipt: Receipt): String {
//          val separator = "\t"
//
//          // Pierwszy wiersz zawiera datę i puste kolumny
//          val headerRow = "${receipt.purchaseDate}$separator$separator$separator$separator$separator$separator\n"
//
//          // Wiersze z produktami - każdy zaczyna się od pustej kolumny (tab)
//          val rows = receipt.products.joinToString("\n") { product ->
//               "$separator${product.name}$separator" +
//                       "${formatPrice(product.priceInCents)}$separator" +
//                       "${product.qty}$separator" +
//                       "${formatPrice(product.totalInCents)}$separator" +
//                       "${product.category}$separator" +
//                       "${product.type}"
//          }
//
//          return headerRow + rows
//     }
//}