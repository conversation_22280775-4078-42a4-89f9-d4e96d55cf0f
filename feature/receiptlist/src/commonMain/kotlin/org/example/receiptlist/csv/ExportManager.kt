//package org.example.receiptlist.csv
//
////import org.example.addReceipt.AddReceiptUiState
////import org.example.addReceipt.UiError
////import org.example.addReceipt.mapper.AddReceiptMapper
//import kotlin.uuid.ExperimentalUuidApi
//
//sealed class ExportState {
//    object Idle : ExportState()
//    object Loading : ExportState()
//    data class Success(val csvContent: String) : ExportState()
//    data class Error(val message: String) : ExportState()
//}
//
//class ExportManager(
//    private val updateState: (transform: (AddReceiptUiState) -> AddReceiptUiState) -> Unit,
//    private val clearError: () -> Unit,
//    private val setError: (UiError) -> Unit,
//    private val getCurrentState: () -> AddReceiptUiState,
//    private val fileManager: FileManager,
//    private val addReceiptMapper: AddReceiptMapper
//) {
//
//    fun updateExportState(newState: ExportState) {
//        updateState { it.copy(operations = it.operations.copy(/*export = newState*/)) }
//    }
//
//    fun resetToIdle() {
//        updateExportState(ExportState.Idle)
//    }
//
//    @OptIn(ExperimentalUuidApi::class)
//    suspend fun exportReceipt(): Result<String> {
//        return try {
//            val currentState = getCurrentState()
//
//            updateExportState(ExportState.Loading)
//            clearError()
//
//            val receipt = addReceiptMapper.mapToDomain(currentState)
//            val csvContent = fileManager.generateCsv(receipt)
//            val fileName = "${receipt.name}-${receipt.receiptSum}${receipt.purchaseDate}.csv"
//
//            fileManager.shareFile(csvContent = csvContent, fileName = fileName)
//            updateExportState(ExportState.Success(csvContent))
//
//            Result.success(csvContent)
//
//        } catch (e: Exception) {
//            updateExportState(ExportState.Error("Błąd eksportu: ${e.message}"))
//            setError(UiError.ErrorMessage("Błąd podczas eksportu: ${e.message}"))
//            Result.failure(e)
//        }
//    }
//
//
//}
//
//// EXPORT RELATED CODE FROM DIFFERENT FILES
///** from KoinModule.kt
//viewModel {
//val uiState = MutableStateFlow(AddReceiptUiState())
//
//AddReceiptViewModel(
//_uiState = uiState,
////            exportManager = ExportManager(
////                updateState = { transform -> uiState.update(transform) },
////                clearError = { uiState.update { it.copy(uiError = null) } },
////                setError = { error -> uiState.update { it.copy(uiError = error) } },
////                getCurrentState = { uiState.value },
////                fileManager = get(),
////                addReceiptMapper = get()
////            ),
//typesCategoriesUseCase = get(),
//scanStateReducer = ScanStateReducer(
//updateState = { transform -> uiState.update(transform) }
//),
//saveReceiptUseCase = get(),
//updateReceiptUseCase = get(),
//duplicateReceiptUseCase = get(),
//getReceiptsUseCase = get(),
//deleteReceiptUseCase = get(),
//addReceiptMapper = get(),
//parseReceiptUseCase = get(),
//openAiResponseMapper = get()
//)
//}
// * */
//
///** from AddReceiptViewModel.kt
//class AddReceiptViewModel(
//private val _uiState: MutableStateFlow<AddReceiptUiState>,
////    private val exportManager: ExportManager,)
//
////            AddReceiptEvent.ShareReceiptCsv -> exportReceipt()
//
////    private fun exportReceipt() {
////        val validationResult = validateProducts()
////        if (!validationResult.isValid) {
////            setUserMessage(validationResult.errorMessage, MessageType.ERROR)
////            return
////        }
////
////        viewModelScope.launch {
////            exportManager.exportReceipt()
////        }
////    }
//
//private fun clearSuccessStates() {
//val currentState = _uiState.value
//
//if (currentState.operations.ocr is OcrState.Success) {
//resetOcrToIdle()
//}
//if (currentState.operations.openAi is OpenAiState.Success) {
//resetOpenAiToIdle()
//}
////        if (currentState.operations.export is ExportState.Success) {
////            exportManager.resetToIdle()
////        }
//if (currentState.operations.save is SaveState.Success) {
//updateOperations { it.copy(save = SaveState.Idle) }
//}
//if (currentState.operations.duplicate is DuplicateState.Success) {
//updateOperations { it.copy(duplicate = DuplicateState.Idle) }
//}
//}
// */
//
///** from AddReceiptUiState.kt
///**
///**
// * All async operation states grouped together
//*/
//@Stable
//data class AsyncOperationsState(
//val scan: ScanState = ScanState.Idle,
//val ocr: OcrState = OcrState.Idle,
//val openAi: OpenAiState = OpenAiState.Idle,
//val save: SaveState = SaveState.Idle,
//val delete: DeleteState = DeleteState.Idle,
//val duplicate: DuplicateState = DuplicateState.Idle,
////    val export: ExportState = ExportState.Idle,
//val loadReceipt: LoadState = LoadState.Idle
//) {
//val isAnyOperationInProgress: Boolean
//get() = ocr is OcrState.Processing ||
//openAi is OpenAiState.Processing ||
////                export is ExportState.Loading ||
//save is SaveState.Saving ||
//delete is DeleteState.Deleting ||
//duplicate is DuplicateState.Duplicating ||
//loadReceipt is LoadState.Loading
//}
// * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
//sealed class ExportState {
//object Idle : ExportState()
//object Loading : ExportState()
//data class Success(val csvContent: String) : ExportState()
//data class Error(val message: String) : ExportState()
//}*/
//
///** from AddReceiptContent.kt
//
//*/
//
////ReceiptImageSection(
////                uiState = uiState,
////                imagePath = uiState.formData.imagePath,
////                ocrResult = uiState.ocrResult,
////                highlightedLine = highlightedProductLine,
////                onCameraClick = {
////                    println("📷 AddReceiptContent: ReceiptImageSection.onCameraClick")
////                    onEvent(AddReceiptEvent.RequestManualScan)
////                },
////                onImageDeleteClick = {
////                    println("🗑️ AddReceiptContent: ReceiptImageSection.onImageDeleteClick")
////                    onEvent(AddReceiptEvent.DeleteImage)
////                    highlightedProductLine = null
////                    overlayMode = OverlayMode.NONE
////                },
////                onRetryOcr = {
////                    println("🔄 AddReceiptContent: ReceiptImageSection.onRetryOcr")
////                    // Retry OCR by re-processing the current image
////                    uiState.formData.imagePath?.let { imagePath ->
////                        scope.launch {
////                            onEvent(AddReceiptEvent.UpdateOcrResult(null)) // Set to loading state
////                            val ocrResult = textRecognizer.recognizeTextWithDetails(imagePath)
////                            onEvent(AddReceiptEvent.UpdateOcrResult(ocrResult))
////                        }
////                    }
////                },
////                onRetryOpenAi = {
////                    println("🔄 AddReceiptContent: ReceiptImageSection.onRetryOpenAi")
////                    onEvent(AddReceiptEvent.RetryOpenAi)
////                },
//////                onRetryExport = {
//////                    println("🔄 AddReceiptContent: ReceiptImageSection.onRetryExport")
//////                    onEvent(AddReceiptEvent.ShareReceiptCsv)
//////                },
////                onClearUserMessage = {
////                    println("❌ AddReceiptContent: ReceiptImageSection.onClearUserMessage")
////                    onEvent(AddReceiptEvent.ClearUserMessage)
////                },
////                onClearSuccessState = {
////                    println("✅ AddReceiptContent: ReceiptImageSection.onClearSuccessState")
////                    onEvent(AddReceiptEvent.ClearSuccessStates)
////                }
////            )
//
////                                DropdownMenuItem(
////                                    text = { Text("Udostępnij CSV") },
////                                    onClick = {
////                                        menuExpanded = false
////                                        onEvent(AddReceiptEvent.ShareReceiptCsv)
////                                    },
////                                    enabled = !uiState.isAnyOperationInProgress,
////                                    leadingIcon = {
////                                        Icon(
////                                            imageVector = Icons.Rounded.CloudUpload,
////                                            contentDescription = "Share CSV",
////                                        )
////                                    }
////                                )
////                                DropdownMenuItem(
////                                    text = { Text("Zduplikuj") },
////                                    onClick = {
////                                        menuExpanded = false
////                                        onEvent(AddReceiptEvent.DuplicateReceipt)
////                                    },
////                                    enabled = !uiState.isAnyOperationInProgress,
////                                    leadingIcon = {
////                                        Icon(
////                                            imageVector = Icons.Rounded.CopyAll,
////                                            contentDescription = "Duplicate receipt",
////                                        )
////                                    }
////                                )
//
///** from ReceiptImageSection*/
////@Composable
////fun ReceiptImageSection(
////    uiState: AddReceiptUiState,
////    imagePath: String? = null,
////    ocrResult: OcrResult? = null,
////    highlightedLine: GroupedTextLine?,
////    onCameraClick: () -> Unit = {},
////    onImageDeleteClick: () -> Unit = {},
////    onRetryOcr: (() -> Unit)? = null,
////    onRetryOpenAi: (() -> Unit)? = null,
//////    onRetryExport: (() -> Unit)? = null,
////    onClearUserMessage: (() -> Unit)? = null,
////    onClearSuccessState: (() -> Unit)? = null
////)
//
////            StatusIndicator(
////                operations = uiState.operations,
////                userMessage = uiState.userMessage,
////                uiError = uiState.uiError,
////                onRetryOcr = onRetryOcr,
////                onRetryOpenAi = onRetryOpenAi,
//////                onRetryExport = onRetryExport,
////                onClearUserMessage = onClearUserMessage,
////                onClearSuccessState = onClearSuccessState
////            )
//
///** from StatusIndicator.kt */
////@Composable
////fun StatusIndicator(
////    modifier: Modifier = Modifier,
////    operations: AsyncOperationsState,
////    userMessage: UserMessage? = null,
////    uiError: UiError? = null, // Legacy support
////    onRetryOcr: (() -> Unit)? = null,
////    onRetryOpenAi: (() -> Unit)? = null,
//////    onRetryExport: (() -> Unit)? = null,
////    onClearUserMessage: (() -> Unit)? = null,
////    onClearSuccessState: (() -> Unit)? = null
////)
//
////    // Extract individual states for easier access
////    val ocrState = operations.ocr
////    val openAiState = operations.openAi
//////    val exportState = operations.export
//
////val currentState: StatusMessage = when {
////        openAiState is OpenAiState.Error -> StatusMessage.Error(openAiState.message, onRetryOpenAi)
////        exportState is ExportState.Error -> StatusMessage.Error(exportState.message, onRetryExport)
//
////        exportState is ExportState.Loading -> StatusMessage.Processing(
////            "Przygotowuję .csv",
////            showProgress = true
////        )
//
////        exportState is ExportState.Success -> StatusMessage.Success(
////            "Zapisz na swoim drive",
////            Icons.Rounded.FileDownload,
////            autoHideAfterMs = 5000
////        )
//
///**
//AddReceiptEvent*/
////    data object ShareReceiptCsv : AddReceiptEvent()