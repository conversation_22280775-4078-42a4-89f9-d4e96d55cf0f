package org.example.receiptlist

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.core.utils.DateUtils
import org.example.shared.component.DateFilter
import org.example.shared.component.DateFilterUtils

data class ReceiptDisplayable(
    val id: String,
    val name: String,
    val purchaseDate: String,
    val receiptSum: String = "0,00",
    val purchaseMethod: String = "",
    val imagePath: String? = null,
    // Type sums for the new ReceiptCard component
    val sumNiezbedne: String = "0,00",
    val sumPrzyjemnosci: String = "0,00",
    val sumOgraniczyc: String = "0,00",
    val segmentWeights: List<Float> = listOf(0f, 0f, 0f),
    val formattedDate: String = "",
    val relativeTime: String = ""
)

data class ReceiptListUiState(
    val receipts: List<ReceiptDisplayable> = emptyList(),
    val currentFilter: DateFilter = DateFilter.All,
    val isFilterExpanded: Boolean = false,
    val availableFilters: List<DateFilter> = DateFilter.getDefaultFilters()
)

class ReceiptListViewModel(
    private val getReceiptsUseCase: GetReceiptsUseCase,
) : ViewModel() {
    private val _uiState = MutableStateFlow(ReceiptListUiState())
    val uiState: StateFlow<ReceiptListUiState> = _uiState.asStateFlow()

    init {
        observeReceipts()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun observeReceipts() {
        viewModelScope.launch {
            // Observe filter changes and fetch receipts accordingly
            _uiState
                .map { it.currentFilter }
                .distinctUntilChanged()
                .flatMapLatest { filter ->
                    val dateRange = DateFilterUtils.getDateRange(filter)
                    getReceiptsUseCase.getReceiptsWithDateFilter(
                        dateRange.startDate,
                        dateRange.endDate
                    )
                }
                .collect { receipts ->
                    _uiState.update { currentState ->
                        currentState.copy(receipts = receipts.map { createReceiptDisplayable(it) })
                    }
                }
        }
    }

    fun onFilterSelected(filter: DateFilter) {
        _uiState.update {
            it.copy(
                currentFilter = filter,
                isFilterExpanded = false
            )
        }
    }

    fun onFilterExpandedChanged(expanded: Boolean) {
        _uiState.update { it.copy(isFilterExpanded = expanded) }
    }

    fun onCustomDateSelected(startDate: String?, endDate: String?) {
        val customFilter = DateFilter.Custom(startDate, endDate)
        _uiState.update {
            it.copy(
                currentFilter = customFilter,
                isFilterExpanded = false
            )
        }
    }
}

fun formatPrice(priceInCents: Long, separator: String = ","): String {
    val baseUnit = priceInCents / 100
    val subunit = priceInCents % 100
    return "$baseUnit$separator${subunit.toString().padStart(2, '0')}"
}

/*
public final data class Receipt(
    val id: String,
    val name: String,
    val products: List<Product>,
    val saveDate: String,
    val purchaseDate: String,
    val receiptSum: Long? = null,
    val purchaseMethod: String = "",
    val productIds: List<String>,
    val imagePath: String? = null
)*/

// Create ReceiptDisplayable from Receipt with calculated type sums
fun createReceiptDisplayable(receipt: Receipt): ReceiptDisplayable {
    val typeOptions = listOf("Niezbędne", "Przyjemności", "Ograniczyć")

    // Calculate sums by type from products
    val sumsByType = receipt.products
        .groupBy { it.type }
        .mapValues { entry ->
            entry.value.sumOf { product ->
                product.totalInCents
            }
        }

    val sumNiezbedne = (sumsByType[typeOptions[0]] ?: 0.0).toLong()
    val sumPrzyjemnosci = (sumsByType[typeOptions[1]] ?: 0.0).toLong()
    val sumOgraniczyc = (sumsByType[typeOptions[2]] ?: 0.0).toLong()

    val totalSumOfProducts = sumNiezbedne + sumPrzyjemnosci + sumOgraniczyc

    // Calculate segment weights for progress bar
    val segmentWeights = listOf(
        if (totalSumOfProducts > 0) (sumNiezbedne.toFloat() / totalSumOfProducts.toFloat()) else 0f,
        if (totalSumOfProducts > 0) (sumPrzyjemnosci.toFloat() / totalSumOfProducts.toFloat()) else 0f,
        if (totalSumOfProducts > 0) (sumOgraniczyc.toFloat() / totalSumOfProducts.toFloat()) else 0f
    )

    // Calculate relative time and formatted date
    val timestamp = DateUtils.isoStringToTimestamp(receipt.purchaseDate)
    val relativeTime = DateUtils.timestampToRelativeTime(timestamp)
    val formattedDate = DateUtils.timestampToDdMmYyyy(timestamp)

    return ReceiptDisplayable(
        id = receipt.id,
        name = receipt.name,
        purchaseDate = receipt.purchaseDate,
        receiptSum = formatPrice(receipt.receiptSum ?: 0),
        purchaseMethod = receipt.purchaseMethod,
        imagePath = receipt.imagePath,
        sumNiezbedne = formatPrice(sumNiezbedne),
        sumPrzyjemnosci = formatPrice(sumPrzyjemnosci),
        sumOgraniczyc = formatPrice(sumOgraniczyc),
        segmentWeights = segmentWeights,
        formattedDate = formattedDate,
        relativeTime = relativeTime
    )
}

// Date utility functions are now in DateUtils



