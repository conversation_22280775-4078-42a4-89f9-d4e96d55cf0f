package org.example.receiptlist.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.FileCopy
import androidx.compose.material.icons.rounded.Favorite
import androidx.compose.material.icons.rounded.MoreHoriz
import androidx.compose.material.icons.rounded.Roofing
import androidx.compose.material.icons.rounded.Warning
import androidx.compose.material.icons.rounded.CloudDone
import androidx.compose.material.icons.rounded.CloudUpload
import androidx.compose.material.icons.rounded.Favorite
import androidx.compose.material.icons.rounded.MoreHoriz
import androidx.compose.material.icons.rounded.Roofing
import androidx.compose.material.icons.rounded.Warning
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import org.example.receiptlist.ReceiptDisplayable
import org.example.shared.ColorPalette
/*

SegmentedWeights and sum of Niezbedne, Przyjemności, Ograniczyć were from viewModel from this functions
private fun transformToUiData(receipt: ReceiptDomain): ReceiptUiData {
    val typeOptions = listOf("Niezbędne", "Przyjemności", "Ograniczyć")

    // Pre-calculate sums by type
    val sumsByType = receipt.products
        .groupBy { it.type }
        .mapValues { entry ->
            entry.value.sumOf { product ->
                product.totalInCents * (product.qty.toDoubleOrNull() ?: 1.0)
            }
        }

    val totalSumOfProducts = sumsByType.values.sum()
    val sumNiezbedne = sumsByType[typeOptions[0]] ?: 0.0
    val sumPrzyjemnosci = sumsByType[typeOptions[1]] ?: 0.0
    val sumOgraniczyc = sumsByType[typeOptions[2]] ?: 0.0

    // Pre-calculate relative time
    val relativeTime = toRelativeTime(receipt.purchaseDate) // we have

    // Pre-calculate formatted date
    val formattedDate = formatDate(receipt.purchaseDate)

    // Pre-calculate segment weights for progress bar
    val segmentWeights = listOf(
        if (totalSumOfProducts > 0) (sumNiezbedne / totalSumOfProducts).toFloat() else 0f,
        if (totalSumOfProducts > 0) (sumPrzyjemnosci / totalSumOfProducts).toFloat() else 0f,
        if (totalSumOfProducts > 0) (sumOgraniczyc / totalSumOfProducts).toFloat() else 0f
    )

    // Convert cents to display values
    val amountDisplay = convertToDisplayValue(receipt.amount) // in our project have formatPrice function
    val sumNiezbedneDisplay = convertToDisplayValue(sumNiezbedne.toLong())
    val sumPrzyjemnosciDisplay = convertToDisplayValue(sumPrzyjemnosci.toLong())
    val sumOgraniczycDisplay = convertToDisplayValue(sumOgraniczyc.toLong())

    return ReceiptUiData(
        id = receipt.id,
        name = receipt.name,
        formattedDate = formattedDate,
        relativeTime = relativeTime,
        amount = amountDisplay,
        sumNiezbedne = sumNiezbedneDisplay,
        sumPrzyjemnosci = sumPrzyjemnosciDisplay,
        sumOgraniczyc = sumOgraniczycDisplay,
        segmentWeights = segmentWeights,
        receipt = receipt // Include original for reference if needed
    )
}
// UI data class to avoid calculations during composition
data class ReceiptUiData(
    val id: String,
    val name: String,
    val formattedDate: String,
    val relativeTime: String,
    val amount: String,
    val sumNiezbedne: String,
    val sumPrzyjemnosci: String,
    val sumOgraniczyc: String,
    val segmentWeights: List<Float>
)

// Convert ReceiptDisplayable to ReceiptUiData
fun ReceiptDisplayable.toReceiptUiData(): ReceiptUiData {
    return ReceiptUiData(
        id = id,
        name = name,
        formattedDate = formattedDate,
        relativeTime = relativeTime,
        amount = formatPrice(receiptSum),
        sumNiezbedne = formatPrice(sumNiezbedne),
        sumPrzyjemnosci = formatPrice(sumPrzyjemnosci),
        sumOgraniczyc = formatPrice(sumOgraniczyc),
        segmentWeights = segmentWeights
    )
}

@Immutable
data class ProductDomain(
    val id: String,
    val name: String,
    val qty: String,
    val priceInCents: Long = 0,
    val totalInCents: Long = 0,
    val category: String,
    val type: String,
    val receiptId: String,
    val purchaseDate: String
)

@Immutable
data class ReceiptDomain(
    val id: String,
    val name: String,
    val products: List<ProductDomain>,
    val saveDate: String,
    val purchaseDate: String,
    val amount: Long,
    val productIds: List<String>,
    val imagePath: String = "" // Added field for storing receipt image path
)
*/


@Composable
fun ClaudeReceiptItem(
    receiptData: ReceiptDisplayable, // we need to refactor and use our ReceiptDisplayable. ReceiptDisplayable need to have sum of products by type
    onClick: () -> Unit,
    onEditClick: () -> Unit,
    onDuplicateClick : () -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }

    Card(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy()
        ),

        ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header section with meta data and options icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Meta data dates
                Text(
                    text = "${receiptData.formattedDate} • ${receiptData.relativeTime}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Row(
                    modifier = Modifier.wrapContentWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // More options menu
                    IconButton(
                        onClick = { showMenu = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.MoreHoriz,
                            contentDescription = "More options",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("Edit") },
                                leadingIcon = {
                                    Icon(
                                        Icons.Default.Edit,
                                        contentDescription = "Edit receipt"
                                    )
                                },
                                onClick = {
                                    showMenu = false
                                    onEditClick()
                                }
                            )
                            DropdownMenuItem(
                                text = { Text("Duplikuj") },
                                leadingIcon = {
                                    Icon(
                                        Icons.Default.FileCopy,
                                        contentDescription = "Duplicate receipt"
                                    )
                                },
                                onClick = {
                                    showMenu = false
                                    onDuplicateClick()
                                }
                            )
                        }
                    }
                }

            }
            // Store and amount
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            ) {
                Text(
                    modifier = Modifier.weight(1.5f),

                    text = receiptData.name.ifEmpty { "Unnamed" },
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Row(
                    modifier = Modifier.weight(2f),
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.End
                ) {
                    Text(
                        text = receiptData.receiptSum.dropLast(2),
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "${receiptData.receiptSum.takeLast(2)} zł",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color.Gray
                    )
                }
            }

            // Type chips in a horizontal row
            Row(
                modifier = Modifier
                    .padding(bottom = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                // Only show types with non-zero amounts
                if (receiptData.sumNiezbedne != "0,00") {
                    TypeChip(
                        icon = Icons.Rounded.Roofing,
                        amount = receiptData.sumNiezbedne,
                        color = ColorPalette.Needs
                    )
                }

                if (receiptData.sumPrzyjemnosci != "0,00") {
                    TypeChip(
                        icon = Icons.Rounded.Favorite,
                        amount = receiptData.sumPrzyjemnosci,
                        color = ColorPalette.Fun
                    )
                }

                if (receiptData.sumOgraniczyc != "0,00") {
                    TypeChip(
                        icon = Icons.Rounded.Warning,
                        amount = receiptData.sumOgraniczyc,
                        color = ColorPalette.Limit
                    )
                }
            }


            // Progress bar at the bottom
            SegmentedProgressBar(
                segments = listOf(
                    SegmentData(receiptData.segmentWeights[0], ColorPalette.Needs),
                    SegmentData(receiptData.segmentWeights[1], ColorPalette.Fun),
                    SegmentData(receiptData.segmentWeights[2], ColorPalette.Limit)
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
            )
        }
    }
}

@Composable
fun TypeChip(
    icon: ImageVector,
    amount: String,
    label: String = "",
    color: Color
) {

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // Icon with colored background
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(color),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = Color.White,
                modifier = Modifier.size(14.dp)
            )
        }
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = amount,
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                fontWeight = FontWeight.SemiBold
            )
            Text(
                text = "zł",
                fontSize = 10.sp,
                color = Color.Gray,
                modifier = Modifier.padding(bottom = 1.dp)
            )
        }

        if (label.isNotEmpty()) {
            Text(
                text = label,
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}


data class SegmentData(val weight: Float, val color: Color)

@Composable
fun SegmentedProgressBar(
    segments: List<SegmentData>,
    modifier: Modifier = Modifier,
    height: Dp = 6.dp,
    strokeWidth: Dp = height,
    backgroundColor: Color = Color(0xFFEEEEEE) // Light background for empty bar
) {
    val totalWeight = remember(segments) {
        segments.sumOf { it.weight.toDouble() }.toFloat()
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(height)
            .clip(RoundedCornerShape(percent = 50))
            .background(backgroundColor) // Add background for empty bar
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val barHeightPx = size.height
            val totalWidthPx = size.width
            var currentX = 0f

            segments.forEach { segment ->
                val segmentWidthPx =
                    if (totalWeight > 0) (segment.weight / totalWeight) * totalWidthPx else 0f
                if (segmentWidthPx > 0) {
                    drawLine(
                        color = segment.color,
                        start = Offset(currentX, barHeightPx / 2),
                        end = Offset(currentX + segmentWidthPx, barHeightPx / 2),
                        strokeWidth = strokeWidth.toPx(),
                        cap = StrokeCap.Round
                    )
                    currentX += segmentWidthPx
                }
            }
        }
    }
}

fun toRelativeTime(
    dateTimeString: String,
    referenceTime: Instant = Clock.System.now()
): String {
    val targetInstant: Instant = try {
        // 1. Main approach: parse as Instant (ISO 8601 format with Z)
        Instant.parse(dateTimeString)
    } catch (e: Exception) {
        try {
            // 2. Try parsing as local datetime (without Z)
            if (dateTimeString.contains("T") && !dateTimeString.endsWith("Z")) {
                val localDateTime = LocalDateTime.parse(dateTimeString)
                localDateTime.toInstant(TimeZone.currentSystemDefault())
            } else {
                throw e // Re-throw to continue to next fallback
            }
        } catch (e2: Exception) {
            // 3. Fallback approach: parse as "DD.MM.YYYY"
            val parts = dateTimeString.split(".")
            if (parts.size == 3) {
                try {
                    val day = parts[0].toInt()
                    val month = parts[1].toInt()
                    val year = parts[2].toInt()

                    // Create LocalDateTime at the start of the day (00:00:00)
                    LocalDateTime(year, month, day, 0, 0, 0, 0)
                        .toInstant(TimeZone.currentSystemDefault())
                } catch (numEx: NumberFormatException) {
                    return dateTimeString
                } catch (argEx: IllegalArgumentException) {
                    return dateTimeString
                } catch (otherEx: Exception) {
                    return dateTimeString
                }
            } else {
                return dateTimeString
            }
        }
    }

    // Convert both instants to local date for calendar day comparison
    val targetLocalDate = targetInstant.toLocalDateTime(TimeZone.currentSystemDefault())
    val referenceLocalDate = referenceTime.toLocalDateTime(TimeZone.currentSystemDefault())

    // Calculate time difference in days
    val duration = referenceTime - targetInstant
    val daysDifference = duration.inWholeDays

    // Check if dates are on the same calendar day
    val isSameDay = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth

    // Check if target date is yesterday (one calendar day before)
    val isYesterday = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth - 1

    // Check if target date is tomorrow (one calendar day after)
    val isTomorrow = targetLocalDate.year == referenceLocalDate.year &&
            targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
            targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth + 1

    // Construct relative response
    return when {
        // Future dates
        isTomorrow -> "Jutro"
        daysDifference < -1 -> {
            val daysInFuture = -daysDifference
            "Za $daysInFuture dni"
        }

        // Current day
        isSameDay -> "Dzisiaj"

        // Past dates
        isYesterday -> "Wczoraj"
        daysDifference > 1 -> "$daysDifference dni temu"

        // This else case should be unreachable with the new calendar-based checks
        else -> dateTimeString
    }
}

