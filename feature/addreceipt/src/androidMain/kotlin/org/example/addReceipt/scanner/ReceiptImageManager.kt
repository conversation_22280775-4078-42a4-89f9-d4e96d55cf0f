package org.example.addReceipt.scanner

import android.content.Context
import org.example.core.domain.service.ImageFileManager
import java.io.File

/**
 * Manages receipt image files lifecycle
 */
class ReceiptImageManager(private val context: Context) : ImageFileManager {
    
    companion object {
        private const val TEMP_PREFIX = "temp_scan_"
        private const val FINAL_PREFIX = "receipt_"
        private const val FILE_EXTENSION = ".jpg"
        private const val CLEANUP_THRESHOLD_MS = 24 * 60 * 60 * 1000L // 24 hours
    }
    
    /**
     * Creates a temporary file for scanning
     */
    fun createTempScanFile(): File {
        val timestamp = System.currentTimeMillis()
        val fileName = "$TEMP_PREFIX$timestamp$FILE_EXTENSION"
        val file = File(context.filesDir, fileName)
        println("ReceiptImageManager: Created temp scan file: ${file.absolutePath}")
        return file
    }
    
    /**
     * Moves temporary file to final location with receipt ID
     */
    override fun moveToFinalLocation(tempFilePath: String, receiptId: String): String? {
        return try {
            val tempFile = File(tempFilePath)
            if (!tempFile.exists()) {
                println("ReceiptImageManager: Temp file does not exist: $tempFilePath")
                return null
            }
            
            val finalFileName = "$FINAL_PREFIX$receiptId$FILE_EXTENSION"
            val finalFile = File(context.filesDir, finalFileName)
            
            println("ReceiptImageManager: Moving temp file to final location")
            println("ReceiptImageManager: From: ${tempFile.absolutePath}")
            println("ReceiptImageManager: To: ${finalFile.absolutePath}")
            
            val success = tempFile.renameTo(finalFile)
            if (success) {
                println("ReceiptImageManager: Successfully moved file, size: ${finalFile.length()} bytes")
                finalFile.absolutePath
            } else {
                // Fallback: copy and delete
                println("ReceiptImageManager: Rename failed, trying copy")
                tempFile.copyTo(finalFile, overwrite = true)
                tempFile.delete()
                
                if (finalFile.exists() && finalFile.length() > 0) {
                    println("ReceiptImageManager: Successfully copied file, size: ${finalFile.length()} bytes")
                    finalFile.absolutePath
                } else {
                    println("ReceiptImageManager: Copy failed")
                    null
                }
            }
        } catch (e: Exception) {
            println("ReceiptImageManager: Error moving file: ${e.message}")
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Deletes a temporary file
     */
    override fun deleteTempFile(tempFilePath: String): Boolean {
        return try {
            val file = File(tempFilePath)
            if (file.exists() && file.name.startsWith(TEMP_PREFIX)) {
                val deleted = file.delete()
                println("ReceiptImageManager: Deleted temp file: $tempFilePath, success: $deleted")
                deleted
            } else {
                println("ReceiptImageManager: Temp file not found or not a temp file: $tempFilePath")
                false
            }
        } catch (e: Exception) {
            println("ReceiptImageManager: Error deleting temp file: ${e.message}")
            false
        }
    }
    
    /**
     * Deletes a receipt image file
     */
    override fun deleteReceiptFile(receiptImagePath: String): Boolean {
        return try {
            val file = File(receiptImagePath)
            if (file.exists()) {
                val deleted = file.delete()
                println("ReceiptImageManager: Deleted receipt file: $receiptImagePath, success: $deleted")
                deleted
            } else {
                println("ReceiptImageManager: Receipt file not found: $receiptImagePath")
                false
            }
        } catch (e: Exception) {
            println("ReceiptImageManager: Error deleting receipt file: ${e.message}")
            false
        }
    }
    
    /**
     * Cleans up old temporary files
     */
    override fun cleanupOldTempFiles() {
        try {
            println("ReceiptImageManager: Starting cleanup of old temp files")
            val currentTime = System.currentTimeMillis()
            val filesDir = context.filesDir
            
            val tempFiles = filesDir.listFiles { file ->
                file.name.startsWith(TEMP_PREFIX) && file.name.endsWith(FILE_EXTENSION)
            } ?: emptyArray()
            
            println("ReceiptImageManager: Found ${tempFiles.size} temp files")
            
            var deletedCount = 0
            tempFiles.forEach { file ->
                val fileAge = currentTime - file.lastModified()
                if (fileAge > CLEANUP_THRESHOLD_MS) {
                    val deleted = file.delete()
                    if (deleted) {
                        deletedCount++
                        println("ReceiptImageManager: Deleted old temp file: ${file.name}, age: ${fileAge / 1000}s")
                    } else {
                        println("ReceiptImageManager: Failed to delete old temp file: ${file.name}")
                    }
                } else {
                    println("ReceiptImageManager: Keeping recent temp file: ${file.name}, age: ${fileAge / 1000}s")
                }
            }
            
            println("ReceiptImageManager: Cleanup completed, deleted $deletedCount files")
        } catch (e: Exception) {
            println("ReceiptImageManager: Error during cleanup: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * Gets all temporary files (for debugging)
     */
    fun getTempFiles(): List<File> {
        return try {
            val filesDir = context.filesDir
            filesDir.listFiles { file ->
                file.name.startsWith(TEMP_PREFIX) && file.name.endsWith(FILE_EXTENSION)
            }?.toList() ?: emptyList()
        } catch (e: Exception) {
            println("ReceiptImageManager: Error getting temp files: ${e.message}")
            emptyList()
        }
    }
}
