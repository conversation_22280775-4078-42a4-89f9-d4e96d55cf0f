package org.example.addReceipt.ui.component

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Psychology
import androidx.compose.material.icons.rounded.Refresh
import androidx.compose.material.icons.rounded.Visibility
import androidx.compose.material.icons.rounded.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import org.example.addReceipt.ui.AsyncOperationsState
import org.example.addReceipt.ui.OcrState
import org.example.addReceipt.ui.OpenAiState
import org.example.addReceipt.ui.SaveState
import org.example.addReceipt.ui.UiError
import org.example.addReceipt.ui.UserMessage

sealed class StatusMessage(val message: String) {
    class Error(
        message: String,
        val onRetry: (() -> Unit)? = null,
        val onClose: (() -> Unit)? = null
    ) : StatusMessage(message)
    class Processing(message: String, val showProgress: Boolean = true) : StatusMessage(message)
    class Success(message: String, val icon: ImageVector, val autoHideAfterMs: Long = 3000) :
        StatusMessage(message)

    object Hidden : StatusMessage("")
}

@Composable
fun StatusIndicator(
    modifier: Modifier = Modifier,
    operations: AsyncOperationsState,
    userMessage: UserMessage? = null,
    uiError: UiError? = null, // Legacy support
    onRetryOcr: (() -> Unit)? = null,
    onRetryOpenAi: (() -> Unit)? = null,
    onClearUserMessage: (() -> Unit)? = null,
    onClearSuccessState: (() -> Unit)? = null
) {
    println("🎨 StatusIndicator: operations=${operations.isAnyOperationInProgress}, userMessage=${userMessage?.message}")

    // Extract individual states for easier access
    val ocrState = operations.ocr
    val openAiState = operations.openAi
    val saveState = operations.save
    val currentState: StatusMessage = when {
        // UserMessage has highest priority
        userMessage != null -> StatusMessage.Error(
            message = userMessage.message,
            onRetry = null, // UserMessage doesn't have retry
            onClose = onClearUserMessage // But has close
        )

        // Then stateful errors with retry capability
        uiError is UiError.ErrorMessage -> StatusMessage.Error(uiError.message)
        ocrState is OcrState.Error -> StatusMessage.Error(ocrState.message, onRetryOcr)
        openAiState is OpenAiState.Error -> StatusMessage.Error(openAiState.message, onRetryOpenAi)

        ocrState is OcrState.Processing -> StatusMessage.Processing(
            "Przetwarzam zdjęcie paragonu...",
            showProgress = true
        )

        openAiState is OpenAiState.Processing -> StatusMessage.Processing(
            "Wyodrębniam dane...",
            showProgress = true
        )



        saveState is SaveState.Saving -> StatusMessage.Processing(
            "Zapisuję paragon...",
            showProgress = true
        )

        saveState is SaveState.Success -> StatusMessage.Success(
            "Paragon został zapisany!",
            Icons.Rounded.Check,
        )

        openAiState is OpenAiState.Success -> StatusMessage.Success(
            "Uzupełniłem dane!",
            Icons.Rounded.Psychology,
        )

        ocrState is OcrState.Success -> StatusMessage.Success(
            "Przetworzyłem tekst z paragonu!",
            Icons.Rounded.Visibility,
        )

        else -> StatusMessage.Hidden
    }

    var isVisible by remember { mutableStateOf(currentState !is StatusMessage.Hidden) }

    // Auto-hide logic for Success states
    LaunchedEffect(currentState) {
        isVisible = currentState !is StatusMessage.Hidden

        if (currentState is StatusMessage.Success && currentState.autoHideAfterMs > 0) {
            delay(currentState.autoHideAfterMs)
            isVisible = false
            // Optionally clear the success state after hiding
            onClearSuccessState?.invoke() //????? TODO ten komponent nie powinien resetować state co nie????? // no ale przy np. export/duplicate niech zresetuje bo nie chce nawigowac do tylu wiec nie potrzebuje success. ??
        }
    }
    Column(
        modifier = modifier
            .background(color = MaterialTheme.colorScheme.background)
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
            .fillMaxWidth()
            .then(if (isVisible) Modifier.wrapContentHeight() else Modifier.height(0.dp))
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        when (currentState) {
            is StatusMessage.Error -> {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    WarningIcon(Icons.Rounded.Warning, true)

                    Text(
                        modifier = Modifier.weight(1f),
                        text = currentState.message,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error
                    )

                    // Retry button if callback is provided
                    currentState.onRetry?.let { retryCallback ->
                        Button(
                            onClick = retryCallback,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer,
                                contentColor = MaterialTheme.colorScheme.onErrorContainer
                            ),
                            modifier = Modifier.weight(0.5f)
                        ) {
                            Icon(
                                imageVector = Icons.Rounded.Refresh,
                                contentDescription = "Retry",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Retry", style = MaterialTheme.typography.labelSmall)
                        }
                    }

                    // Close button if callback is provided
                    currentState.onClose?.let { closeCallback ->
                        Button(
                            onClick = closeCallback,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer,
                                contentColor = MaterialTheme.colorScheme.onErrorContainer
                            ),
                            modifier = Modifier.weight(0.5f)
                        ) {
                            Icon(
                                imageVector = Icons.Rounded.Close,
                                contentDescription = "Close",
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text("Close", style = MaterialTheme.typography.labelSmall)
                        }
                    }
                }
            }

            is StatusMessage.Processing -> {

                if (currentState.showProgress) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                }

                Text(
                    text = currentState.message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }


            is StatusMessage.Success -> {

                val randomConfettiStyle: ConfettiStyle = listOf(
                    ConfettiStyle.Rounded,
                    ConfettiStyle.Linear,
                    ConfettiStyle.TriangleLinear,
                    ConfettiStyle.Mixed
                ).random()

                SuccessIconWithConfetti(
                    icon = currentState.icon,
                    trigger = true,
                    onAnimationEnd = {},
                    confettiStyle = randomConfettiStyle,
                    enableDrift = true
                )

                Text(
                    text = currentState.message,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )

            }

            else -> Unit
        }
    }
}