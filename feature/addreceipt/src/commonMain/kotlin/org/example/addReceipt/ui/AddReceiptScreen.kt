package org.example.addReceipt.ui

import AddReceiptContent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import org.example.addReceipt.AddReceiptEvent
import org.example.addReceipt.AddReceiptViewModel
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun AddReceiptScreen(
    receiptId: String? = null,
    isEditMode: Boolean = false,
    navigateBack: () -> Unit = {},
    startWithScan: Boolean = false
) {
    val viewModel = koinViewModel<AddReceiptViewModel>()
    val uiState by viewModel.uiState.collectAsState()
    val scope = rememberCoroutineScope()

    // Load receipt for editing if receiptId is provided
    DisposableEffect(receiptId) {
        if (receiptId != null && isEditMode) {
            viewModel.onEvent(AddReceiptEvent.LoadReceipt(receiptId))
        } else if (receiptId != null && !isEditMode) {
            viewModel.onEvent(AddReceiptEvent.LoadReceiptForDuplication(receiptId))
        }
        onDispose { }
    }

    AddReceiptContent(
        uiState = uiState,
        onEvent = viewModel::onEvent,
        scope = scope,
        navigateBack = navigateBack,
        startWithScan = startWithScan
    )
}