package org.example.addReceipt.ui.model

import androidx.compose.runtime.Stable
import org.example.addReceipt.getCurrentTimeString

/**
 * Receipt Form data that user directly inputs.
 */
@Stable
data class ReceiptFormData(
    val storeName: String = "",
    val storeAddress: String = "",
    val receiptSumInCents: Long = 0,
    val purchaseMethod: String = "",
    val purchaseDate: String = getCurrentTimeString(),
    val imagePath: String? = null,
    val products: List<ProductFormData> = listOf(ProductFormData())
)