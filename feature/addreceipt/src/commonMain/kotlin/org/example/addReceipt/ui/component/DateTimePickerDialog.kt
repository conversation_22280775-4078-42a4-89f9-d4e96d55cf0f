package org.example.addReceipt.ui.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import kotlinx.datetime.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateTimePickerDialog(
    initialDateTime: String = "",
    onDateTimeSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    // Parse initial date or use current time
    val (initialDate, initialTime) = remember(initialDateTime) {
        parseInitialDateTime(initialDateTime)
    }

    var selectedDate by remember { mutableStateOf(initialDate) }
    var selectedTime by remember { mutableStateOf(initialTime) }
    var showDatePicker by remember { mutableStateOf(true) }
    var showTimePicker by remember { mutableStateOf(false) }

    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = selectedDate.toEpochDays() * 24 * 60 * 60 * 1000L
        )

        DatePickerDialog(
            onDismissRequest = onDismiss,
            confirmButton = {
                TextButton(onClick = {
                    // Update selected date from picker state
                    datePickerState.selectedDateMillis?.let { millis ->
                        selectedDate = Instant.fromEpochMilliseconds(millis)
                            .toLocalDateTime(TimeZone.currentSystemDefault())
                            .date
                    }

                    showDatePicker = false
                    showTimePicker = true
                }) {
                    Text("Dalej")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("Anuluj")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }

    if (showTimePicker) {
        val timePickerState = rememberTimePickerState(
            initialHour = selectedTime.hour,
            initialMinute = selectedTime.minute
        )

        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("Wybierz godzinę") },
            text = {
                Column {
                    TimePicker(state = timePickerState)
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    // Update selected time from picker state
                    selectedTime = LocalTime(timePickerState.hour, timePickerState.minute)

                    // Combine date and time
                    val dateTime = LocalDateTime(selectedDate, selectedTime)
                    val instant = dateTime.toInstant(TimeZone.currentSystemDefault())

                    onDateTimeSelected(instant.toString())
                    onDismiss()
                }) {
                    Text("Zatwierdź")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("Anuluj")
                }
            }
        )
    }
}

/**
 * Parses initial datetime string and returns LocalDate and LocalTime
 * Handles formats like "2025-07-09T14:49" from OpenAI
 */
private fun parseInitialDateTime(dateTimeString: String): Pair<LocalDate, LocalTime> {
    if (dateTimeString.isEmpty()) {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return Pair(now.date, now.time)
    }

    return try {
        // Parse as local time, not UTC
        val localDateTime = if (dateTimeString.contains("T")) {
            val withSeconds = if (dateTimeString.count { it == ':' } == 1) {
                "${dateTimeString}:00"
            } else {
                dateTimeString
            }
            LocalDateTime.parse(withSeconds)
        } else {
            LocalDateTime.parse(dateTimeString)
        }

        Pair(localDateTime.date, localDateTime.time)
    } catch (e: Exception) {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        Pair(now.date, now.time)
    }
}
// This one returned 2h hours difference
/*private fun parseInitialDateTime(dateTimeString: String): Pair<LocalDate, LocalTime> {
    print("TimeZone.currentSystemDefault(): ${TimeZone.currentSystemDefault()}")
    if (dateTimeString.isEmpty()) {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return Pair(now.date, now.time)
    }

    return try {
        // Handle OpenAI format: "2025-07-09T14:49"
        val instant = if (dateTimeString.contains("T") && !dateTimeString.endsWith("Z")) {
            // Add seconds and Z if missing
            val withSeconds = if (dateTimeString.count { it == ':' } == 1) {
                "${dateTimeString}:00Z"
            } else {
                "${dateTimeString}Z"
            }
            Instant.parse(withSeconds)
        } else {
            Instant.parse(dateTimeString)
        }

        val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
        Pair(localDateTime.date, localDateTime.time)
    } catch (e: Exception) {
        // Fallback to current time
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        Pair(now.date, now.time)
    }
}*/

/**
 * Utility function to format datetime for OpenAI compatibility
 */
fun formatDateTimeForOpenAI(instant: Instant): String {
    val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    return "${localDateTime.date}T${
        localDateTime.time.hour.toString().padStart(2, '0')
    }:${localDateTime.time.minute.toString().padStart(2, '0')}"
}
