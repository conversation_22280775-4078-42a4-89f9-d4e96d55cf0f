package org.example.addReceipt.ui.component

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.Path
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.PI
import kotlin.random.Random
@Composable
fun RainConfettiOverlay(
    modifier: Modifier = Modifier,
    isActive: <PERSON><PERSON>an,
    confettiStyle: ConfettiStyle = ConfettiStyle.Rounded,
    enableDrift: Boolean = false,
    durationMs: Int = 2000
) {
    val particles = remember { mutableStateListOf<ConfettiParticle>() }
    val elapsedTime = remember { mutableFloatStateOf(0f) }

    // Używamy BoxWithConstraints, aby uzyskać dostęp do wymiarów kontenera.
    BoxWithConstraints(modifier = modifier.fillMaxSize()) {
        // Pobieramy szerokość kontenera w pikselach.
        val widthPx = constraints.maxWidth.toFloat()

        // LaunchedEffect jest teraz uruchamiany, gdy 'isActive' lub szerokość się zmieni.
        LaunchedEffect(isActive, widthPx) {
            if (isActive && widthPx > 0) {
                particles.clear()
                elapsedTime.floatValue = 0f

                repeat(50) {
                    // Używamy dynamicznie pobranej szerokości do pozycjonowania cząstek.
                    val xPos = Random.nextFloat() * widthPx
                    val speed = Random.nextDouble(150.0, 300.0)
                    val color = Color.hsv(Random.nextFloat() * 360f, 0.8f, 1f)
                    val drift = if (enableDrift) Random.nextFloat() * 2f - 1f else 0f

                    val styleToUse = if (confettiStyle == ConfettiStyle.Mixed)
                        listOf(
                            ConfettiStyle.Rounded,
                            ConfettiStyle.Linear,
                            ConfettiStyle.TriangleLinear
                        ).random() else confettiStyle

                    val angle = PI / 2 // Spadanie prosto w dół

                    // Tworzymy cząstkę - tutaj można by użyć 'styleToUse' do tworzenia różnych typów
                    val particle = RoundedForRainParticle(
                        angle, speed, color, size = Random.nextDouble(6.0, 10.0).dp, drift = drift
                    ).apply {
                        // Nadpisujemy pozycję startową X, aby cząstki pojawiały się losowo na całej szerokości.
                        this.startX = xPos
                    }

                    particles.add(particle)
                }

                // Uruchomienie animacji
                val animatable = Animatable(0f)
                animatable.animateTo(
                    targetValue = 1f,
                    animationSpec = tween(durationMillis = durationMs, easing = LinearEasing)
                ) {
                    elapsedTime.floatValue = value
                }

                particles.clear()
            }
        }

        Canvas(modifier = Modifier.fillMaxSize()) {
            val elapsed = elapsedTime.floatValue
            // Usunięto problematyczną linię: particles.removeAll(...)

            particles.forEach { particle ->
                val t = particle.progress(elapsed)
                // Używamy zapisanej w cząstce pozycji startowej 'startX'
                val x = (particle as? ConfettiParticleWithX)?.startX ?: (size.width / 2)
                val distance = (particle.speed * t).toFloat()

                val position = Offset(
                    x = x + particle.drift * t * 50f,
                    y = distance
                )
                val alpha = 1f - t

                // Rysowanie różnych typów cząstek
                when (particle) {
                    is RoundedForRainParticle -> drawCircle(
                        color = particle.color.copy(alpha = alpha),
                        radius = particle.size.toPx() / 2,
                        center = position
                    )
                    is RoundedParticle -> drawCircle(
                        color = particle.color.copy(alpha = alpha),
                        radius = particle.size.toPx() / 2,
                        center = position
                    )
                    is LinearParticle -> {
                        val direction = Offset(0f, 1f)
                        val halfLength = particle.length.toPx() / 2
                        drawLine(
                            color = particle.color.copy(alpha = alpha),
                            start = position - direction * halfLength,
                            end = position + direction * halfLength,
                            strokeWidth = particle.size.toPx(),
                            cap = StrokeCap.Round
                        )
                    }
                    is TriangleLinearParticle -> {
                        val path = androidx.compose.ui.graphics.Path().apply {
                            val baseWidth = particle.size.toPx()
                            val tipWidth = baseWidth * 0.3f
                            val halfLength = particle.length.toPx() / 2
                            moveTo(position.x - baseWidth / 2, position.y - halfLength)
                            lineTo(position.x + baseWidth / 2, position.y - halfLength)
                            lineTo(position.x + tipWidth / 2, position.y + halfLength)
                            lineTo(position.x - tipWidth / 2, position.y + halfLength)
                            close()
                        }
                        drawPath(path, color = particle.color.copy(alpha = alpha))
                    }
                }
            }
        }
    }
}

interface ConfettiParticleWithX {
    var startX: Float
}

data class RoundedForRainParticle(
    override val angle: Double,
    override val speed: Double,
    override val color: Color,
    val size: Dp,
    override val drift: Float = 0f,
    override var startX: Float = 0f
) : ConfettiParticle(angle, speed, color, drift), ConfettiParticleWithX {
    override fun progress(elapsedTimeSec: Float): Float = elapsedTimeSec.coerceIn(0f, 1f)
}

