package org.example.addReceipt.ui.component

import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Camera
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.scanner.OcrResult
import org.example.addReceipt.scanner.getBitmapSize
import org.example.addReceipt.scanner.rememberImageLoader
import org.example.core.domain.model.GroupedTextLine

enum class OverlayMode {
    NONE,
    TEXT_BOXES,
    LINES,
}
@Composable
fun ReceiptImageSection(
    uiState: AddReceiptUiState,
    imagePath: String? = null,
    ocrResult: OcrResult? = null,
    highlightedLine: GroupedTextLine?,
    onCameraClick: () -> Unit = {},
    onImageDeleteClick: () -> Unit = {},
    onRetryOcr: (() -> Unit)? = null,
    onRetryOpenAi: (() -> Unit)? = null,
    onClearUserMessage: (() -> Unit)? = null,
    onClearSuccessState: (() -> Unit)? = null
) {
    val currentImagePath = remember(imagePath) { imagePath }
    val scrollState = rememberScrollState()
    val cardHeight = if (currentImagePath.isNullOrBlank()) 100.dp else 150.dp

    // Animacja dla przejścia między stanami
    val imageVisible = !currentImagePath.isNullOrEmpty()
    val imageAlpha by animateFloatAsState(
        targetValue = if (imageVisible) 1f else 0f,
        animationSpec = tween(
            durationMillis = 500,
            easing = EaseInOutCubic
        ),
        label = "imageAlpha"
    )

    val placeholderAlpha by animateFloatAsState(
        targetValue = if (imageVisible) 0f else 1f,
        animationSpec = tween(
            durationMillis = 500,
            easing = EaseInOutCubic
        ),
        label = "placeholderAlpha"
    )

    // Animacja skalowania dla efektu "zoom in"
    val imageScale by animateFloatAsState(
        targetValue = if (imageVisible) 1f else 0.8f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "imageScale"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
            .height(cardHeight),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.7f))
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            StatusIndicator(
                operations = uiState.operations,
                userMessage = uiState.userMessage,
                uiError = uiState.uiError,
                onRetryOcr = onRetryOcr,
                onRetryOpenAi = onRetryOpenAi,
                onClearUserMessage = onClearUserMessage,
                onClearSuccessState = onClearSuccessState
            )
            BoxWithConstraints(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color.LightGray.copy(alpha = 0.3f))
                    .then(
                        if (currentImagePath.isNullOrBlank()) Modifier.clickable(onClick = onCameraClick)
                        else Modifier
                    ),
                contentAlignment = Alignment.Center
            ) {
                // Użyj Crossfade do animacji przejścia między stanami
                Crossfade(
                    targetState = imageVisible,
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = EaseInOutCubic
                    ),
                    label = "imageTransition"
                ) { isImageVisible ->
                    if (!isImageVisible) {
                        // Placeholder
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .alpha(placeholderAlpha)
                                .scale(if (imageVisible) 0.9f else 1f),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            // Animacja ikony kamery
                            val cameraIconScale by animateFloatAsState(
                                targetValue = if (imageVisible) 0.8f else 1f,
                                animationSpec = spring(
                                    dampingRatio = Spring.DampingRatioMediumBouncy,
                                    stiffness = Spring.StiffnessMedium
                                ),
                                label = "cameraIconScale"
                            )

                            Icon(
                                Icons.Rounded.Camera,
                                "Camera",
                                modifier = Modifier
                                    .size(36.dp)
                                    .scale(cameraIconScale),
                                tint = Color.Gray
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                "Kliknij aby zeskanować paragon",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color.Gray
                            )
                        }
                    } else {
                        // Obrazek
                        if (!currentImagePath.isNullOrEmpty()) {
                            var imageDisplaySize by remember { mutableStateOf(IntSize.Zero) }
                            val imageLoader = rememberImageLoader()
                            val displayImagePath = ocrResult?.imagePathForDisplay ?: currentImagePath
                            val bitmapSize = getBitmapSize(displayImagePath)

                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .verticalScroll(scrollState)
                                    .alpha(imageAlpha)
                                    .scale(imageScale)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .onSizeChanged { newSize ->
                                            if (imageDisplaySize != newSize) {
                                                imageDisplaySize = newSize
                                            }
                                        }
                                ) {
                                    imageLoader.LoadImage(
                                        path = displayImagePath,
                                        contentDescription = "Receipt image",
                                        contentScale = ContentScale.FillWidth,
                                        modifier = Modifier.fillMaxWidth()
                                    )

                                    // Highlight overlay z animacją
                                    if (imageDisplaySize != IntSize.Zero &&
                                        highlightedLine != null &&
                                        bitmapSize != IntSize.Zero
                                    ) {
                                        //todo animowane alphanie dzuala
                                        val highlightAlpha by animateFloatAsState(
                                            targetValue = .8f,
                                            animationSpec = tween(
                                                durationMillis = 1000,
                                                delayMillis = 400
                                            ),
                                            label = "highlightAlpha"
                                        )

                                        Canvas(
                                            modifier = Modifier
                                                .matchParentSize()
                                                .alpha(highlightAlpha)
                                        ) {
                                            val box = highlightedLine.myBoundingBox
                                            val actualScale = this.size.width / bitmapSize.width.toFloat()

                                            val scaledLeft = box.left * actualScale
                                            val scaledTop = box.top * actualScale
                                            val scaledRight = box.right * actualScale
                                            val scaledBottom = box.bottom * actualScale

                                            drawRect(
                                                color = Color.Blue.copy(alpha = highlightAlpha),
                                                topLeft = Offset(scaledLeft, scaledTop),
                                                size = Size(
                                                    scaledRight - scaledLeft,
                                                    scaledBottom - scaledTop
                                                ),
                                                style = Stroke(width = 2.dp.toPx())
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Delete button z animacją - poza Crossfade żeby był zawsze na górze
                if (imageVisible) {
                    val deleteButtonAlpha by animateFloatAsState(
                        targetValue = if (imageVisible) 1f else 0f,
                        animationSpec = tween(
                            durationMillis = 300,
                            delayMillis = 400
                        ),
                        label = "deleteButtonAlpha"
                    )

                    val deleteButtonOffset by animateIntAsState(
                        targetValue = if (imageVisible) 0 else 50,
                        animationSpec = tween(
                            durationMillis = 300,
                            delayMillis = 400
                        ),
                        label = "deleteButtonOffset"
                    )

                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(16.dp)
                            .offset(x = deleteButtonOffset.dp)
                            .alpha(deleteButtonAlpha)
                    ) {
                        IconButton(
                            onClick = onImageDeleteClick,
                            Modifier
                                .size(36.dp)
                                .clip(CircleShape)
                                .background(Color(0xFFE6EFFF).copy(alpha = 0.5f))
                        ) {
                            Icon(Icons.Rounded.Close, "Delete", tint = Color(0xFF6B9EFF))
                        }
                    }
                }
            }
        }
    }
}


/////// Tylko animacja Card:
/*
@Composable
fun ReceiptImageSection(
    uiState: AddReceiptUiState,
    imagePath: String? = null,
    ocrResult: OcrResult? = null,
    highlightedLine: GroupedTextLine?,
    onCameraClick: () -> Unit = {},
    onImageDeleteClick: () -> Unit = {},
    onRetryOcr: (() -> Unit)? = null,
    onRetryOpenAi: (() -> Unit)? = null,
    onRetryExport: (() -> Unit)? = null,
    onRetrySave: (() -> Unit)? = null,
    onClearSuccessState: (() -> Unit)? = null
) {
    val currentImagePath = remember(imagePath) { imagePath }
    val scrollState = rememberScrollState()
    val cardHeight = if (currentImagePath.isNullOrBlank()) 100.dp else 150.dp

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioLowBouncy,
                    stiffness = Spring.StiffnessLow
                )
            )
            .height(cardHeight),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White.copy(alpha = 0.7f))
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            StatusIndicator(
                operations = uiState.operations,
                userMessage = uiState.userMessage,
                uiError = uiState.uiError,
                onRetryOcr = onRetryOcr,
                onRetryOpenAi = onRetryOpenAi,
                onRetryExport = onRetryExport,
                onClearUserMessage = onClearUserMessage,
                onClearSuccessState = onClearSuccessState
            )
            BoxWithConstraints(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(16.dp))
                    .background(Color.LightGray.copy(alpha = 0.3f))
                    .then(
                        if (currentImagePath.isNullOrBlank()) Modifier.clickable(onClick = onCameraClick)
                        else Modifier
                    ),
                contentAlignment = Alignment.Center
            ) {
                val imageVisible = !currentImagePath.isNullOrEmpty()

                if (!currentImagePath.isNullOrEmpty()) {
                    var imageDisplaySize by remember { mutableStateOf(IntSize.Zero) }
                    val imageLoader = rememberImageLoader()
                    val displayImagePath = ocrResult?.imagePathForDisplay ?: currentImagePath
                    val bitmapSize = getBitmapSize(displayImagePath)

                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .verticalScroll(scrollState)
                    ) {

                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .onSizeChanged { newSize ->
                                    if (imageDisplaySize != newSize) {
                                        imageDisplaySize = newSize
                                    }
                                }
                        ) {

                            imageLoader.LoadImage(
                                path = displayImagePath,
                                contentDescription = "Receipt image",
                                contentScale = ContentScale.FillWidth,
                                modifier = Modifier.fillMaxWidth()
                            )

                            // Highlight overlay
                            if (imageDisplaySize != IntSize.Zero &&
                                highlightedLine != null &&
                                bitmapSize != IntSize.Zero
                            ) {

                                Canvas(modifier = Modifier.matchParentSize()) {
                                    val box = highlightedLine.myBoundingBox
                                    val actualScale = this.size.width / bitmapSize.width.toFloat()

                                    val scaledLeft = box.left * actualScale
                                    val scaledTop = box.top * actualScale
                                    val scaledRight = box.right * actualScale
                                    val scaledBottom = box.bottom * actualScale

                                    drawRect(
                                        color = Color.Blue.copy(alpha = 0.8f),
                                        topLeft = Offset(scaledLeft, scaledTop),
                                        size = Size(
                                            scaledRight - scaledLeft,
                                            scaledBottom - scaledTop
                                        ),
                                        style = Stroke(width = 2.dp.toPx())
                                    )
                                }
                            }
                        }
                    }
                } else {
                    // Placeholder when no image
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(Icons.Rounded.Camera, "Camera", Modifier.size(36.dp), Color.Gray)
                        Text(
                            "Kliknij aby zeskanować paragon",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color.Gray
                        )
                    }
                }

                // Delete button
                if (!currentImagePath.isNullOrEmpty()) {
                    Box(Modifier.align(Alignment.TopEnd).padding(16.dp)) {
                        IconButton(
                            onClick = onImageDeleteClick,
                            Modifier.size(36.dp).clip(CircleShape)
                                .background(Color(0xFFE6EFFF).copy(alpha = 0.5f))
                        ) {
                            Icon(Icons.Rounded.Close, "Delete", tint = Color(0xFF6B9EFF))
                        }
                    }
                }
            }
        }
    }
}*/
