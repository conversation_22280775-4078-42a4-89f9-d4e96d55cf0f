package org.example.addReceipt.mapper

import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.ui.OpenAiErrorType
import org.example.addReceipt.ui.OpenAiState
import org.example.addReceipt.ui.model.ProductFormData
import org.example.addReceipt.parseOpenAiDateTime
import org.example.core.domain.exception.APIException
import org.example.core.domain.exception.NetworkException
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.exception.TimeoutException
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.model.ai.ParsedReceiptData

/**
 * Mapper responsible for converting OpenAI responses to UI state updates
 * This class contains no dependencies and is purely functional
 */
class OpenAiResponseMapper {

    /**
     * Maps ParsedReceiptData to UI state transformation function
     * @param parsedData Data from OpenAI API
     * @param originalOcrLines Original OCR lines for matching
     * @return Function that transforms AddReceiptUiState
     */
    fun mapToUiStateUpdate(
        parsedData: ParsedReceiptData,
        originalOcrLines: List<GroupedTextLine>
    ): (AddReceiptUiState) -> AddReceiptUiState {
        val productUiItems = createProductUiItemsFromParsedData(parsedData, originalOcrLines)
        
        return { currentState ->
            currentState.copy(
                formData = currentState.formData.copy(
                    storeName = parsedData.storeName,
                    storeAddress = parsedData.storeAddress,
                    purchaseDate = parseOpenAiDateTime(parsedData.purchaseDateTime ?: ""),
                    receiptSumInCents = (parsedData.receiptSum * 100).toLong(),
                    purchaseMethod = parsedData.purchaseMethod ?: "",
                    products = productUiItems.ifEmpty { listOf(ProductFormData()) }
                ),
                operations = currentState.operations.copy(openAi = OpenAiState.Success)
            )
        }
    }

    /**
     * Maps exception to OpenAI error state
     * @param exception Exception from OpenAI processing
     * @return OpenAiState.Error with appropriate message and type
     */
    fun mapError(exception: Throwable): OpenAiState.Error {
        val (message, type) = categorizeError(exception)
        return OpenAiState.Error(message = message, errorType = type)
    }

    /**
     * Creates ProductDisplayable items from parsed data and matches them with OCR lines
     */
    private fun createProductUiItemsFromParsedData(
        parsedData: ParsedReceiptData,
        originalOcrLines: List<GroupedTextLine>
    ): List<ProductFormData> {
        return parsedData.products.mapNotNull { parsedProduct ->
            val productNameLower = parsedProduct.name.lowercase().trim()

            if (productNameLower.isBlank()) {
                return@mapNotNull null
            }

            val matchingOcrLine = originalOcrLines.find { ocrLine ->
                val ocrLineTextLower = ocrLine.text.lowercase().trim()
                ocrLineTextLower.contains(productNameLower)
            }

            ProductFormData(
                name = parsedProduct.name,
                qty = parsedProduct.quantity.toString(),
                priceInCents = (parsedProduct.unitPrice * 100).toLong(),
                totalInCents = (parsedProduct.totalPrice * 100).toLong(),
                category = parsedProduct.category,
                type = parsedProduct.type,
                totalNotBlank = (parsedProduct.totalPrice * 100).toLong() > 0L,
                ocrGroupedTextLine = matchingOcrLine
            )
        }
    }

    /**
     * Categorizes exceptions into appropriate error types with user-friendly messages
     */
    private fun categorizeError(exception: Throwable): Pair<String, OpenAiErrorType> {
        return when (exception) {
            is TimeoutException -> {
                "Przekroczono czas oczekiwania. Sprawdź połączenie internetowe i spróbuj ponownie." to OpenAiErrorType.TIMEOUT_ERROR
            }
            is NetworkException -> {
                "Błąd połączenia. Sprawdź dostęp do internetu i spróbuj ponownie." to OpenAiErrorType.NETWORK_ERROR
            }
            is APIException -> {
                "Błąd serwera OpenAI. Spróbuj ponownie za chwilę." to OpenAiErrorType.API_ERROR
            }
            is ParseReceiptException -> {
                "Nie udało się przetworzyć danych z paragonu. Spróbuj ponownie lub wprowadź dane ręcznie." to OpenAiErrorType.PARSING_ERROR
            }
            else -> {
                "Wystąpił nieoczekiwany błąd: ${exception.message}" to OpenAiErrorType.UNKNOWN_ERROR
            }
        }
    }
}
