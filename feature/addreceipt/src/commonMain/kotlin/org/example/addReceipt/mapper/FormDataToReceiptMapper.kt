package org.example.addReceipt.mapper

import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.getCurrentTimeString
import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

/**
 * Mapper responsible for converting UI state to domain models
 */
class FormDataToReceiptMapper {

    /**
     * Maps AddReceiptUiState to Receipt domain model
     * @param state Current UI state
     * @param receiptId Optional receipt ID (for editing mode), if null generates new UUID
     * @return Receipt domain model
     */
    @OptIn(ExperimentalUuidApi::class)
    fun mapToDomain(state: AddReceiptUiState, receiptId: String? = null): Receipt {
        val finalReceiptId = receiptId ?: Uuid.random().toHexString()
        
        return Receipt(
            id = finalReceiptId,
            name = state.formData.storeName,
            products = state.formData.products.map { productDisplayable ->
                Product(
                    id = productDisplayable.id,
                    name = productDisplayable.name,
                    qty = productDisplayable.qty,
                    priceInCents = productDisplayable.priceInCents,
                    totalInCents = productDisplayable.totalInCents,
                    category = productDisplayable.category,
                    type = productDisplayable.type,
                    purchaseDate = state.formData.purchaseDate,
                    receiptId = finalReceiptId,
                    ocrGroupedTextLine = productDisplayable.ocrGroupedTextLine
                )
            },
            saveDate = getCurrentTimeString(),
            purchaseDate = state.formData.purchaseDate,
            receiptSum = state.formData.receiptSumInCents,
            purchaseMethod = state.formData.purchaseMethod,
            productIds = state.formData.products.map { it.id },
            imagePath = state.formData.imagePath
        )
    }
}
