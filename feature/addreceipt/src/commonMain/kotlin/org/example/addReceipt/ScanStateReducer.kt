package org.example.addReceipt

import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.ui.ScanState

/**
 * Handles scan state transitions in the presentation layer
 * Follows Clean Architecture principles by focusing only on state management
 */
class ScanStateReducer(
    private val updateState: (transform: (AddReceiptUiState) -> AddReceiptUiState) -> Unit
) {

    /**
     * Requests automatic scanning (triggered by startWithScan parameter)
     */
    fun requestAutoScan(currentState: AddReceiptUiState) {
        if (currentState.operations.scan == ScanState.Idle) {
            updateState { it.copy(operations = it.operations.copy(scan = ScanState.PendingAutoScan)) }
        }
    }

    /**
     * Requests manual scanning (triggered by camera button)
     */
    fun requestManualScan() {
        updateState { it.copy(operations = it.operations.copy(scan = ScanState.PendingAutoScan)) }
    }

    /**
     * Marks scan as started to prevent re-triggering
     */
    fun markScanStarted() {
        updateState { it.copy(operations = it.operations.copy(scan = ScanState.Scanning)) }
    }

    /**
     * Marks scan as completed (prevents auto-scan re-triggering after process death)
     */
    fun markScanCompleted() {
        updateState { it.copy(operations = it.operations.copy(scan = ScanState.Completed)) }
    }

    /**
     * Resets scan state to allow new scanning
     */
    fun resetScanState() {
        updateState { it.copy(operations = it.operations.copy(scan = ScanState.Idle)) }
    }
}
