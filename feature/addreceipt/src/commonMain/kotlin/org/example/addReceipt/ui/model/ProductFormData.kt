package org.example.addReceipt.ui.model

import androidx.compose.runtime.Stable
import org.example.core.domain.model.GroupedTextLine
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

/**
 * !!!!!! MOVED FROM AddReceiptUiState.kt
 * Form data that user directly inputs.
 */
@Stable
data class ProductFormData @OptIn(ExperimentalUuidApi::class) constructor(
    val id: String = Uuid.random().toHexString(),
    val name: String = "",
    val qty: String = "1",
    val priceInCents: Long = 0,
    val totalInCents: Long = 0,
    val category: String = "Jedzenie",
    val type: String = "Niezbędne",
    val purchaseDate: String = "",
    val totalNotBlank: Boolean = false,
    val ocrGroupedTextLine: GroupedTextLine? = null
)