package org.example.addReceipt.mapper

import org.example.addReceipt.getCurrentTimeString
import org.example.addReceipt.ui.model.ProductFormData
import org.example.addReceipt.ui.model.ReceiptFormData
import org.example.core.domain.model.Product
import org.example.core.domain.usecase.receipt.ReceiptEditData
import org.example.core.domain.usecase.receipt.ScreenMode
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class ReceiptToFormDataMapper {

    fun prepareFormData(receiptEditData: ReceiptEditData): ReceiptFormData {
        val receipt = receiptEditData.receipt
        val mode = receiptEditData.mode

        val products = receipt.products.map { product ->
            mapProductToProductForm(product, mode)
        }.ifEmpty { listOf(ProductFormData()) }

        return ReceiptFormData(
            storeName = when (mode) {
                ScreenMode.DUPLICATE -> "Kopia - ${receipt.name}"
                ScreenMode.EDIT -> receipt.name
            },
            receiptSumInCents = receipt.receiptSum ?: 0L,
            purchaseDate = when (mode) {
                ScreenMode.DUPLICATE -> getCurrentTimeString()
                ScreenMode.EDIT -> receipt.purchaseDate
            },
            purchaseMethod = receipt.purchaseMethod,
            imagePath = when (mode) {
                ScreenMode.EDIT -> receipt.imagePath
                ScreenMode.DUPLICATE -> null
            },
            products = products
        )
    }

    fun determineUiStateMode(receiptEditData: ReceiptEditData): UiStateUpdate {
        return when (receiptEditData.mode) {
            ScreenMode.EDIT -> UiStateUpdate(
                isEditMode = true,
                editingReceiptId = receiptEditData.receipt.id
            )
            ScreenMode.DUPLICATE -> UiStateUpdate(
                isEditMode = false,
                editingReceiptId = null
            )
        }
    }

    @OptIn(ExperimentalUuidApi::class)
    private fun mapProductToProductForm(
        product: Product,
        mode: ScreenMode
    ): ProductFormData {
        return ProductFormData(
            id = when (mode) {
                ScreenMode.EDIT -> product.id
                ScreenMode.DUPLICATE -> Uuid.random().toHexString()
            },
            name = product.name,
            qty = product.qty,
            priceInCents = product.priceInCents,
            totalInCents = product.totalInCents,
            category = product.category,
            type = product.type,
            purchaseDate = when (mode) {
                ScreenMode.EDIT -> product.purchaseDate
                ScreenMode.DUPLICATE -> getCurrentTimeString()
            },
            totalNotBlank = product.totalInCents > 0L,
            ocrGroupedTextLine = when (mode) {
                ScreenMode.EDIT -> product.ocrGroupedTextLine
                ScreenMode.DUPLICATE -> null
            }
        )
    }
}

data class UiStateUpdate(
    val isEditMode: Boolean,
    val editingReceiptId: String?
)