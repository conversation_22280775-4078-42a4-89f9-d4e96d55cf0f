package org.example.addReceipt

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.core.domain.usecase.receipt.SaveReceiptUseCase
import org.example.core.domain.usecase.receipt.UpdateReceiptUseCase
import org.example.core.domain.usecase.receipt.DeleteReceiptUseCase
import org.example.core.domain.usecase.ai.ParseReceiptUseCase
import org.example.addReceipt.mapper.FormDataToReceiptMapper
import org.example.addReceipt.mapper.OpenAiResponseMapper
import kotlinx.coroutines.flow.update
import org.example.addReceipt.mapper.ReceiptToFormDataMapper
import org.example.addReceipt.scanner.DocumentScanner
import org.example.addReceipt.scanner.OcrResult
import org.example.addReceipt.scanner.TextRecognizerML
import org.example.addReceipt.ui.AddReceiptUiState
import org.example.addReceipt.ui.AsyncOperationsState
import org.example.addReceipt.ui.DeleteState
import org.example.addReceipt.ui.LoadState
import org.example.addReceipt.ui.MessageType
import org.example.addReceipt.ui.OcrState
import org.example.addReceipt.ui.OpenAiErrorType
import org.example.addReceipt.ui.OpenAiState
import org.example.addReceipt.ui.model.ProductFormData
import org.example.addReceipt.ui.model.ReceiptFormData
import org.example.addReceipt.ui.SaveState
import org.example.addReceipt.ui.ScanState
import org.example.addReceipt.ui.ScreenContext
import org.example.addReceipt.ui.UiState
import org.example.addReceipt.ui.UserMessage
import org.example.core.domain.model.GroupedTextLine
import org.example.core.domain.usecase.receipt.LoadReceiptUseCase
import org.example.core.domain.usecase.receipt.ReceiptEditData
import org.example.core.domain.usecase.receipt.ScreenMode


class AddReceiptViewModel(
    private val _uiState: MutableStateFlow<AddReceiptUiState>,
    private val typesCategoriesUseCase: GetTypesAndCategoriesUseCase,
    private val scanStateReducer: ScanStateReducer,
    private val saveReceiptUseCase: SaveReceiptUseCase,
    private val updateReceiptUseCase: UpdateReceiptUseCase,
    private val loadReceiptUseCase: LoadReceiptUseCase,
    private val receiptToFormDataMapper: ReceiptToFormDataMapper,
    private val deleteReceiptUseCase: DeleteReceiptUseCase,
    private val formDataToReceiptMapper: FormDataToReceiptMapper,
    private val parseReceiptUseCase: ParseReceiptUseCase,
    private val openAiResponseMapper: OpenAiResponseMapper,
) : ViewModel() {

    val uiState: StateFlow<AddReceiptUiState> get() = _uiState

    init {
        observeCategories()
        observeTypes()
    }


    private fun observeCategories() {
        viewModelScope.launch {
            typesCategoriesUseCase.getCategories().collect { categories ->
                println("📂 AddReceiptViewModel: observeCategories - received ${categories.size} categories")
                updateContext { it.copy(categories = categories) }
            }
        }
    }

    private fun observeTypes() {
        viewModelScope.launch {
            typesCategoriesUseCase.getTypes().collect { types ->
                println("🏷️ AddReceiptViewModel: observeTypes - received ${types.size} types")
                updateContext { it.copy(types = types) }
            }
        }
    }

    fun onEvent(event: AddReceiptEvent) {
        when (event) {
            is AddReceiptEvent.LoadReceipt -> {
                loadReceipt(event.receiptId, ScreenMode.EDIT)
            }

            is AddReceiptEvent.LoadReceiptForDuplication -> {
                loadReceipt(event.receiptId, ScreenMode.DUPLICATE)
            }

            // Image & OCR events
            is AddReceiptEvent.UpdateReceiptImage -> {
                updateReceiptImage(event.result)
            }

            is AddReceiptEvent.UpdateOcrResult -> {
                viewModelScope.launch {
                    handleOcrResult(event.ocrResult)
                    if (event.ocrResult != null) {
                        processOcrResultIfReady()
                    }
                }
            }

            is AddReceiptEvent.DeleteImage -> {
                deleteImage()
            }

            // Receipt basic info events
            is AddReceiptEvent.UpdateStoreName -> updateStoreName(event.storeName)
            is AddReceiptEvent.UpdateAddress -> updateStoreAddress(event.address)
            is AddReceiptEvent.UpdatePurchaseDate -> updatePurchaseDate(event.purchaseDate)
            is AddReceiptEvent.UpdateReceiptSum -> updateReceiptSum(event.receiptSum)
            is AddReceiptEvent.UpdatePurchaseMethod -> updatePurchaseMethod(event.purchaseMethod)
            AddReceiptEvent.ShowDateTimePicker -> {
                println("📅 AddReceiptViewModel: ShowDateTimePicker")
                updateUi { it.copy(showDateTimePicker = true) }
            }

            AddReceiptEvent.HideDateTimePicker -> {
                println("📅 AddReceiptViewModel: HideDateTimePicker")
                updateUi { it.copy(showDateTimePicker = false) }
            }

            // Product events
            is AddReceiptEvent.UpdateProductName -> updateProductName(
                event.productId,
                event.name
            )

            is AddReceiptEvent.UpdateProductQty -> updateProductQuantity(
                event.productId,
                event.qty
            )

            is AddReceiptEvent.UpdateProductPrice -> updateProductPrice(
                event.productId,
                event.priceInCents
            )

            is AddReceiptEvent.UpdateProductTotal -> updateProductTotal(
                event.productId,
                event.totalInCents
            )

            is AddReceiptEvent.UpdateProductCategory -> updateProductCategory(
                event.productId,
                event.category
            )

            is AddReceiptEvent.UpdateProductType -> updateProductType(
                event.productId,
                event.type
            )

            AddReceiptEvent.AddNewProduct -> addNewProduct()
            is AddReceiptEvent.DeleteProduct -> deleteProduct(event.product.id)

            // Action events
            AddReceiptEvent.SaveReceipt -> saveReceipt()
            AddReceiptEvent.ClearAllInputs -> clearState()
            AddReceiptEvent.ClearError -> clearError()
            AddReceiptEvent.ClearUserMessage -> clearUserMessage()

            // Retry events
            AddReceiptEvent.RetryOpenAi -> retryOpenAi()
            AddReceiptEvent.ClearSuccessStates -> clearSuccessStates()
            AddReceiptEvent.DeleteReceipt -> deleteReceipt()
            AddReceiptEvent.RequestAutoScan -> {
                scanStateReducer.requestAutoScan(_uiState.value)
            }

            AddReceiptEvent.RequestManualScan -> {
                scanStateReducer.requestManualScan()
            }

            AddReceiptEvent.ScanStarted -> {
                scanStateReducer.markScanStarted()
            }

            AddReceiptEvent.ScanCompleted -> {
                scanStateReducer.markScanCompleted()
            }

            AddReceiptEvent.ResetScanState -> {
                scanStateReducer.resetScanState()
            }

            is AddReceiptEvent.ExecuteScan -> {
                executeScan(event.documentScanner, event.textRecognizer)
            }
        }
    }

    private fun updateStoreName(storeName: String) {
        println("🏪 AddReceiptViewModel: updateStoreName - '$storeName'")
        updateFormData { it.copy(storeName = storeName) }
    }

    private fun updateStoreAddress(address: String) {
        println("📍 AddReceiptViewModel: updateStoreAddress - '$address'")
        updateFormData { it.copy(storeAddress = address) }
    }

    private fun updatePurchaseDate(purchaseDate: String) {
        println("📅 AddReceiptViewModel: updatePurchaseDate - '$purchaseDate'")
        updateFormData { it.copy(purchaseDate = purchaseDate) }
    }

    private fun updateReceiptSum(receiptSumInCents: Long) {
        println("💰 AddReceiptViewModel: updateReceiptSum - $receiptSumInCents cents")
        updateFormData { it.copy(receiptSumInCents = receiptSumInCents) }
    }

    private fun updatePurchaseMethod(purchaseMethod: String) {
        println("💳 AddReceiptViewModel: updatePurchaseMethod - '$purchaseMethod'")
        updateFormData { it.copy(purchaseMethod = purchaseMethod) }
    }

    private fun clearError() {
        _uiState.update { it.copy(uiError = null) }
    }

    private fun clearUserMessage() {
        _uiState.update { it.copy(userMessage = null) }
    }

    private fun setUserMessage(message: String, type: MessageType = MessageType.ERROR) {
        _uiState.update {
            it.copy(userMessage = UserMessage(message = message, type = type))
        }
    }
/** Resetting ViewModel to default empty state but breserving categories and types */
    private fun clearState() {
        println("🧹 AddReceiptViewModel: clearState - resetting to default state")
        _uiState.update { currentState ->
            AddReceiptUiState().copy(
                context = currentState.context,
                operations = currentState.operations.copy(scan = ScanState.Idle)
            )
        }
    }

    // Product management methods (moved from ProductManager)
    private fun updateProductField(
        productId: String,
        transform: (ProductFormData) -> ProductFormData
    ) {
        println("📦 AddReceiptViewModel: updateProductField - productId: $productId")
        updateFormData { currentFormData ->
            currentFormData.copy(
                products = currentFormData.products.map { product ->
                    if (product.id == productId) transform(product) else product
                }
            )
        }
    }

    private fun updateProductName(productId: String, name: String) {
        updateProductField(productId) { it.copy(name = name) }
    }

    private fun updateProductQuantity(productId: String, qty: String) {
        updateProductField(productId) { it.copy(qty = qty) }
    }

    private fun updateProductPrice(productId: String, priceInCents: Long) {
        updateProductField(productId) { it.copy(priceInCents = priceInCents) }
    }

    private fun updateProductTotal(productId: String, totalInCents: Long) {
        updateProductField(productId) { product ->
            product.copy(
                totalInCents = totalInCents,
                totalNotBlank = totalInCents > 0L
            )
        }
    }

    private fun updateProductCategory(productId: String, category: String) {
        updateProductField(productId) { it.copy(category = category) }
    }

    private fun updateProductType(productId: String, type: String) {
        updateProductField(productId) { it.copy(type = type) }
    }

    private fun addNewProduct() {
        println("➕ AddReceiptViewModel: addNewProduct")
        updateFormData { currentFormData ->
            currentFormData.copy(products = currentFormData.products + ProductFormData())
        }
    }

    private fun deleteProduct(productId: String) {
        println("➖ AddReceiptViewModel: deleteProduct - productId: $productId")
        updateFormData { currentFormData ->
            if (currentFormData.products.size > 1) {
                currentFormData.copy(
                    products = currentFormData.products.filter { it.id != productId }
                )
            } else {
                currentFormData
            }
        }
    }

    private fun validateProducts(): ValidationResult {
        val currentState = _uiState.value
        val invalidProducts = currentState.formData.products.filter { !it.totalNotBlank }

        return if (invalidProducts.isNotEmpty()) {
            ValidationResult(
                isValid = false,
                errorMessage = "Suma w produktach nie może być pusta: ${invalidProducts.size}"
            )
        } else {
            ValidationResult(isValid = true)
        }
    }

    private fun loadReceipt(receiptId: String, mode: ScreenMode) {
        viewModelScope.launch {
            println("📥 AddReceiptViewModel: loadReceipt ($mode) - receiptId: $receiptId")
            clearError()
            updateOperations { it.copy(loadReceipt = LoadState.Loading) }

            val result = loadReceiptUseCase.loadReceipt(receiptId, mode)
            result
                .onSuccess { receiptEditData ->
                    populateStateFromReceiptData(receiptEditData)
                    updateOperations { it.copy(loadReceipt = LoadState.Idle) }
                }
                .onFailure { exception ->
                    handleReceiptLoadError(exception.message ?: "Nieznany błąd")
                }
        }
    }


    private fun populateStateFromReceiptData(receiptEditData: ReceiptEditData) {
        println("📄 AddReceiptViewModel: populateStateFromReceiptData ${receiptEditData.mode.name.lowercase()} - ${receiptEditData.receipt.name}")

        val formData = receiptToFormDataMapper.prepareFormData(receiptEditData)
        val uiStateUpdate = receiptToFormDataMapper.determineUiStateMode(receiptEditData)

        updateFormData { formData }
        updateUi { currentUi ->
            currentUi.copy(
                isEditMode = uiStateUpdate.isEditMode,
                editingReceiptId = uiStateUpdate.editingReceiptId
            )
        }
    }

    private fun handleReceiptLoadError(message: String) {
        updateOperations { it.copy(loadReceipt = LoadState.Idle) }
        _uiState.update {
            it.copy(
                userMessage = UserMessage(
                    message = message,
                    type = MessageType.ERROR
                )
            )
        }
    }

    private fun deleteReceipt() {
        viewModelScope.launch {
            val receiptId = _uiState.value.ui.editingReceiptId
            if (receiptId != null) {
                println("🗑️ AddReceiptViewModel: deleteReceipt - receiptId: $receiptId")
                updateOperations { it.copy(delete = DeleteState.Deleting) }

                try {
                    deleteReceiptUseCase(receiptId)
                    updateOperations { it.copy(delete = DeleteState.Success) }
                } catch (e: Exception) {
                    val errorMessage = "Wystąpił błąd podczas usuwania: ${e.message}"
                    updateOperations { it.copy(delete = DeleteState.Idle) }
                    _uiState.update {
                        it.copy(
                            userMessage = UserMessage(
                                message = errorMessage,
                                type = MessageType.ERROR
                            )
                        )
                    }
                }
            }
        }
    }

    private fun processOcrResultIfReady() {
        val currentState = _uiState.value
        println("AddReceiptViewModel: processOcrResultIfReady called")

        if (isOcrResultReady(currentState)) {
            val ocrData = getOcrData(currentState)
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                println("AddReceiptViewModel: OCR data ready, text length: ${ocrText.length}, lines: ${originalOcrLines.size}")
                viewModelScope.launch {
                    println("AddReceiptViewModel: Calling parseReceiptWithAi...")
                    parseReceiptWithAi(ocrText, originalOcrLines)
                }
            } else {
                println("AddReceiptViewModel: OCR data is null")
            }
        } else {
            println("AddReceiptViewModel: OCR result not ready")
            updateOperations {
                it.copy(
                    openAi = OpenAiState.Error(
                        message = "OCR returned empty text.",
                        errorType = OpenAiErrorType.PARSING_ERROR
                    )
                )
            }
        }
    }

    private fun saveReceipt() {
        val validationResult = validateProducts()
        if (!validationResult.isValid) {
            setUserMessage(validationResult.errorMessage, MessageType.ERROR)
            return
        }
        viewModelScope.launch {
            updateOperations { it.copy(save = SaveState.Saving) }
            val currentState = _uiState.value

            try {
                val receiptId =
                    if (currentState.ui.isEditMode) currentState.ui.editingReceiptId else null
                val receipt = formDataToReceiptMapper.mapToDomain(currentState, receiptId)

                val result =
                    if (currentState.ui.isEditMode && currentState.ui.editingReceiptId != null) {
                        updateReceiptUseCase(receipt)
                    } else {
                        saveReceiptUseCase(receipt)
                    }

                if (result.isSuccess) {
                    updateOperations { it.copy(save = SaveState.Success) }
                } else {
                    val exception = result.exceptionOrNull()
                    val errorMessage = if (currentState.ui.isEditMode) {
                        "Wystąpił błąd podczas aktualizacji: ${exception?.message}"
                    } else {
                        "Wystąpił błąd podczas zapisywania: ${exception?.message}"
                    }
                    updateOperations { it.copy(save = SaveState.Idle) }
                    _uiState.update {
                        it.copy(
                            userMessage = UserMessage(
                                message = errorMessage,
                                type = MessageType.ERROR
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                val errorMessage = if (_uiState.value.ui.isEditMode) {
                    "Wystąpił błąd podczas aktualizacji: ${e.message}"
                } else {
                    "Wystąpił błąd podczas zapisywania: ${e.message}"
                }
                updateOperations { it.copy(save = SaveState.Idle) }
                _uiState.update {
                    it.copy(
                        userMessage = UserMessage(
                            message = errorMessage,
                            type = MessageType.ERROR
                        )
                    )
                }
            }
        }
    }

    private fun retryOpenAi() {
        val currentState = _uiState.value
        if (isOcrResultReady(currentState)) {
            val ocrData = getOcrData(currentState)
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                viewModelScope.launch {
                    parseReceiptWithAi(ocrText, originalOcrLines)
                }
            }
        }
    }

    private fun executeScan(
        documentScanner: DocumentScanner,
        textRecognizer: TextRecognizerML
    ) {
        viewModelScope.launch {
            try {
                scanStateReducer.markScanStarted()

                val result = documentScanner.scanDocument()
                if (result != null) {
                    // Update image and start OCR
                    updateReceiptImage(result)
                    handleOcrResult(null) // Set to loading state

                    val ocrResult = textRecognizer.recognizeTextWithDetails(result)
                    handleOcrResult(ocrResult)

                    // Process OCR result and trigger OpenAI if ready
                    if (ocrResult != null) {
                        println("AddReceiptViewModel: executeScan - OCR result received, calling processOcrResultIfReady")
                        processOcrResultIfReady()
                    } else {
                        println("AddReceiptViewModel: executeScan - OCR result is null")
                    }

                    scanStateReducer.markScanCompleted()
                } else {
                    // Scan was cancelled, reset to idle
                    scanStateReducer.resetScanState()
                }
            } catch (e: Exception) {
                println("AddReceiptViewModel: Error during scan: ${e.message}")
                scanStateReducer.resetScanState()
            }
        }
    }

    private fun clearSuccessStates() {
        val currentState = _uiState.value

        if (currentState.operations.ocr is OcrState.Success) {
            resetOcrToIdle()
        }
        if (currentState.operations.openAi is OpenAiState.Success) {
            resetOpenAiToIdle()
        }
        if (currentState.operations.save is SaveState.Success) {
            updateOperations { it.copy(save = SaveState.Idle) }
        }
    }

    // OCR management methods (moved from OcrManager)
    private fun updateReceiptImage(imagePath: String?) {
        println("📷 AddReceiptViewModel: updateReceiptImage - path: $imagePath")
        updateFormData { it.copy(imagePath = imagePath) }
        updateOperations { currentOps ->
            currentOps.copy(
                ocr = if (imagePath != null) OcrState.Processing else OcrState.Idle,
                scan = if (imagePath == null) ScanState.Idle else currentOps.scan
            )
        }
        // Clear OCR result when image changes
        _uiState.update { it.copy(ocrResult = null) }
    }

    private fun deleteImage() {
        updateReceiptImage(null)
        handleOcrResult(null)
        resetOcrToIdle()
    }

    private fun handleOcrResult(ocrResult: OcrResult?) {
        when {
            ocrResult == null -> {
                updateOcrState(OcrState.Processing, null)
            }

            ocrResult.errorMessage != null -> {
                updateOcrState(OcrState.Error(ocrResult.errorMessage), ocrResult)
            }

            else -> {
                updateOcrState(OcrState.Success, ocrResult)
            }
        }
    }

    private fun updateOcrState(
        newState: OcrState,
        result: OcrResult?
    ) {
        println("🔍 AddReceiptViewModel: updateOcrState - state: $newState")
        updateOperations { it.copy(ocr = newState) }
        _uiState.update { currentState ->
            currentState.copy(
                ocrResult = result,
                formData = result?.imagePathForDisplay?.let { imagePath ->
                    currentState.formData.copy(imagePath = imagePath)
                } ?: currentState.formData
            )
        }
    }

    private fun isOcrResultReady(currentState: AddReceiptUiState): Boolean {
        return currentState.ocrResult?.groupedWordsIntoLines?.isNotBlank() == true
    }

    private fun getOcrData(currentState: AddReceiptUiState): Pair<String, List<GroupedTextLine>>? {
        val ocrResult = currentState.ocrResult

        return if (ocrResult != null && ocrResult.groupedWordsIntoLines.isNotBlank()) {
            ocrResult.groupedWordsIntoLines to ocrResult.groupedLinesWithDetails
        } else {
            null
        }
    }

    private fun resetOcrToIdle() {
        updateOcrState(OcrState.Idle, null)
    }

    // OpenAI management methods (moved from OpenAiManager)
    private suspend fun parseReceiptWithAi(
        ocrText: String,
        originalOcrLines: List<GroupedTextLine>
    ) {
        try {
            println("AddReceiptViewModel: Starting parseReceiptWithAi with OCR text length: ${ocrText.length}")
            updateOperations { it.copy(openAi = OpenAiState.Processing) }
            clearError()

            println("AddReceiptViewModel: Calling parseReceiptUseCase...")
            parseReceiptUseCase(ocrText)
                .onSuccess { parsedData ->
                    println("AddReceiptViewModel: ParseReceiptUseCase SUCCESS - applying mapper")
                    val stateUpdateFn =
                        openAiResponseMapper.mapToUiStateUpdate(parsedData, originalOcrLines)
                    _uiState.update(stateUpdateFn)
                    println("AddReceiptViewModel: UI state updated successfully")
                }
                .onFailure { error ->
                    println("AddReceiptViewModel: ParseReceiptUseCase FAILURE - ${error.message}")
                    val errorState = openAiResponseMapper.mapError(error)
                    updateOperations { it.copy(openAi = errorState) }
                }
        } catch (e: Exception) {
            println("AddReceiptViewModel: parseReceiptWithAi EXCEPTION - ${e.message}")
            val errorState = openAiResponseMapper.mapError(e)
            updateOperations { it.copy(openAi = errorState) }
        }
    }

    private fun resetOpenAiToIdle() {
        println("🔄 AddReceiptViewModel: resetOpenAiToIdle")
        _uiState.update {
            it.copy(operations = it.operations.copy(openAi = OpenAiState.Idle))
        }
    }

    private fun updateFormData(transform: (ReceiptFormData) -> ReceiptFormData) {
        println("📝 AddReceiptViewModel: updateFormData")
        _uiState.update { currentState ->
            currentState.copy(formData = transform(currentState.formData))
        }
    }

    private fun updateOperations(transform: (AsyncOperationsState) -> AsyncOperationsState) {
        println("⚙️ AddReceiptViewModel: updateOperations")
        _uiState.update { currentState ->
            currentState.copy(operations = transform(currentState.operations))
        }
    }

    private fun updateContext(transform: (ScreenContext) -> ScreenContext) {
        println("🌍 AddReceiptViewModel: updateContext")
        _uiState.update { currentState ->
            currentState.copy(context = transform(currentState.context))
        }
    }

    private fun updateUi(transform: (UiState) -> UiState) {
        println("🎨 AddReceiptViewModel: updateUi")
        _uiState.update { currentState ->
            currentState.copy(ui = transform(currentState.ui))
        }
    }
}

data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String = ""
)