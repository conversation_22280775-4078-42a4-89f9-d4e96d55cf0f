package org.example.addReceipt.ui

import androidx.compose.runtime.Stable
import kotlinx.datetime.Clock
import org.example.addReceipt.scanner.OcrResult
import org.example.addReceipt.ui.model.ReceiptFormData
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type

/**
 * New grouped UI state structure
 * Replaces the old flat structure with organized groups
 */
@Stable
data class AddReceiptUiState(
    val formData: ReceiptFormData = ReceiptFormData(),
    val operations: AsyncOperationsState = AsyncOperationsState(),
    val context: ScreenContext = ScreenContext(),
    val ui: UiState = UiState(),
    val userMessage: UserMessage? = null,

    // Legacy fields for backward compatibility during migration
    val uiError: UiError? = null,
    val ocrResult: OcrResult? = null
) {
    val isAnyOperationInProgress: Boolean
        get() = operations.isAnyOperationInProgress
}

/**
 * All async operation states grouped together
 */
@Stable
data class AsyncOperationsState(
    val scan: ScanState = ScanState.Idle,
    val ocr: OcrState = OcrState.Idle,
    val openAi: OpenAiState = OpenAiState.Idle,
    val save: SaveState = SaveState.Idle,
    val delete: DeleteState = DeleteState.Idle,
//    val duplicate: DuplicateState = DuplicateState.Idle,
    val loadReceipt: LoadState = LoadState.Idle
) {
    val isAnyOperationInProgress: Boolean
        get() = ocr is OcrState.Processing ||
                openAi is OpenAiState.Processing ||
                save is SaveState.Saving ||
                delete is DeleteState.Deleting ||
//                duplicate is DuplicateState.Duplicating ||
                loadReceipt is LoadState.Loading
}

/**
 * Static/context data loaded at startup
 */
@Stable
data class ScreenContext(
    val categories: List<Category> = emptyList(),
    val types: List<Type> = emptyList()
)

/**
 * UI-specific state (not data)
 */
@Stable
data class UiState(
    val showDateTimePicker: Boolean = false,
    val isEditMode: Boolean = false,
    val editingReceiptId: String? = null
)

sealed class UiError {
    data class ErrorMessage(val message: String) : UiError()
}

// Temporary classes for migration - will be moved to new structure
data class UserMessage(
    val id: Long = Clock.System.now().toEpochMilliseconds(),
    val message: String,
    val type: MessageType = MessageType.ERROR
)

enum class MessageType {
    ERROR, SUCCESS, INFO, WARNING
}

/**
 * Load state with error for retry capability
 */
sealed class LoadState {
    object Idle : LoadState()
    object Loading : LoadState()
    data class Error(val message: String) : LoadState()
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class OcrState {
    object Idle : OcrState()
    object Processing : OcrState()
    object Success : OcrState()
    data class Error(val message: String) : OcrState()
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class OpenAiState {
    object Idle : OpenAiState()
    object Processing : OpenAiState()
    object Success : OpenAiState()
    data class Error(val message: String, val errorType: OpenAiErrorType) : OpenAiState()
}

enum class OpenAiErrorType {
    NETWORK_ERROR,
    TIMEOUT_ERROR,
    API_ERROR,
    PARSING_ERROR,
    UNKNOWN_ERROR
}


/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class SaveState {
    object Idle : SaveState()
    object Saving : SaveState()
    object Success : SaveState()
    // Error removed - handled by UserMessage
}

sealed class DeleteState {
    object Idle : DeleteState()
    object Deleting : DeleteState()
    object Success : DeleteState()
    // Error removed - handled by UserMessage
}

//sealed class DuplicateState {
//    object Idle : DuplicateState()
//    object Duplicating : DuplicateState()
//    object Success : DuplicateState()
//    // Error removed - handled by UserMessage
//}

/**
 * Represents the state of document scanning functionality
 * This consolidates the previous startScan and scanAlreadyStarted flags
 */
sealed class ScanState {
    object Idle : ScanState()                    // No scanning requested
    object PendingAutoScan : ScanState()         // Auto-scan requested but not started
    object Scanning : ScanState()                // Currently scanning
    object Completed : ScanState()               // Scan completed (prevents re-triggering)
}