package org.example.addReceipt

import org.example.core.domain.service.ImageFileManager

/**
 * iOS implementation of ImageFileManager
 * iOS doesn't need complex file management as it handles app lifecycle differently
 */
class ImageFileManagerIos : ImageFileManager {
    
    override fun moveToFinalLocation(tempFilePath: String, receiptId: String): String? {
        // iOS doesn't need to move files, return the same path
        println("ImageFileManagerIos: No file moving needed on iOS, returning same path: $tempFilePath")
        return tempFilePath
    }
    
    override fun deleteTempFile(tempFilePath: String): Boolean {
        // iOS handles file cleanup automatically
        println("ImageFileManagerIos: No temp file deletion needed on iOS")
        return true
    }
    
    override fun deleteReceiptFile(receiptImagePath: String): Boolean {
        // iOS handles file cleanup automatically
        println("ImageFileManagerIos: No receipt file deletion needed on iOS")
        return true
    }
    
    override fun cleanupOldTempFiles() {
        // iOS handles file cleanup automatically
        println("ImageFileManagerIos: No cleanup needed on iOS")
    }
}
