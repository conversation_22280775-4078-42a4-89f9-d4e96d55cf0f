package org.example.data.remote

import kotlinx.serialization.json.Json
import org.example.core.domain.OpenAIService
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.model.ai.ParsedReceipt
import org.example.core.domain.model.ai.ParsedReceiptData
import org.example.core.domain.repository.AIRepository
import org.example.data.mapper.toDomainModel
import org.example.data.remote.service.OpenAIRequestBuilder

class AIRepositoryImpl(
    private val openAIService: OpenAIService,
    private val requestBuilder: OpenAIRequestBuilder
) : AIRepository {

    override suspend fun parseReceiptText(
        ocrText: String,
        availableCategories: List<String>,
        availableTypes: List<String>
    ): ParsedReceiptData? {
        println("AIRepositoryImpl: parseReceiptText called with ${availableCategories.size} categories, ${availableTypes.size} types")

        val request = requestBuilder.buildParseReceiptRequest(
            ocrText = ocrText,
            categories = availableCategories,
            types = availableTypes
        )
        println("AIRepositoryImpl: Built request, calling openAIService.parseReceipt...")

        val response = openAIService.parseReceipt(request)
        println("AIRepositoryImpl: Got response from OpenAI service")

        val content = response.choices.firstOrNull()?.message?.content
        if (content == null) {
            println("AIRepositoryImpl: No content in response")
            return null
        }

        println("AIRepositoryImpl: Got content from OpenAI, length: ${content.length}")

        return try {
            // Map OpenAI response to domain model
            val parsedReceipt = Json.decodeFromString<ParsedReceipt>(content)
            val domainModel = parsedReceipt.toDomainModel()
            println("AIRepositoryImpl: Successfully parsed to domain model - storeName: ${domainModel.storeName}")
            domainModel
        } catch (e: Exception) {
            println("AIRepositoryImpl: Failed to parse OpenAI response: ${e.message}")
            throw ParseReceiptException("Failed to parse OpenAI response", e)
        }
    }
}