package org.example.data.remote.service

import io.ktor.client.HttpClient
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import kotlinx.serialization.json.Json
import org.example.core.domain.OpenAIService
import org.example.core.domain.exception.APIException
import org.example.core.domain.exception.NetworkException
import org.example.core.domain.exception.TimeoutException
import org.example.core.domain.model.ai.OpenAiErrorResponse
import org.example.core.domain.model.ai.OpenAiRequest
import org.example.core.domain.model.ai.OpenAiResponse
import org.example.data.remote.service.OPENAI_API_KEY

class OpenAIServiceImpl(
    private val httpClient: HttpClient
) : OpenAIService {

    override suspend fun parseReceipt(request: OpenAiRequest): OpenAiResponse {
        try {
            println("OpenAIServiceImpl: Making request to OpenAI API...")
            val httpResponse = httpClient.post("https://api.openai.com/v1/chat/completions") {
                header(HttpHeaders.Authorization, "Bearer $OPENAI_API_KEY")
                contentType(ContentType.Application.Json)
                setBody(request)
            }

            println("OpenAIServiceImpl: Got HTTP response, status: ${httpResponse.status}")
            val rawResponseText = httpResponse.bodyAsText()
            println("OpenAIServiceImpl: Response body length: ${rawResponseText.length}")

            return try {
                val response = Json.decodeFromString<OpenAiResponse>(rawResponseText)
                println("OpenAIServiceImpl: Successfully parsed OpenAI response")
                response
            } catch (e: Exception) {
                println("OpenAIServiceImpl: Failed to parse as success response, trying error response...")
                // Try to parse as error response
                val errorResponse = Json.decodeFromString<OpenAiErrorResponse>(rawResponseText)
                throw APIException("OpenAI API Error: ${errorResponse.error.message}")
            }
        } catch (e: HttpRequestTimeoutException) {
            throw TimeoutException("Request timed out", e)
        } catch (e: ConnectTimeoutException) {
            throw NetworkException("Connection timeout", e)
        } catch (e: Exception) {
            when {
                e.message?.contains("network", ignoreCase = true) == true ->
                    throw NetworkException("Network error", e)
                else -> throw APIException("Unexpected error: ${e.message}", e)
            }
        }
    }
}