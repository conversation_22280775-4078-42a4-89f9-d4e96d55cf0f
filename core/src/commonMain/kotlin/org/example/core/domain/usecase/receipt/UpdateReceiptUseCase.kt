package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.domain.service.ImageFileManager

/**
 * Use case for updating an existing receipt with its products
 * Handles image file management during update operation
 */
class UpdateReceiptUseCase(
    private val receiptRepository: ReceiptRepository,
    private val imageFileManager: ImageFileManager? = null
) {
    suspend operator fun invoke(receipt: Receipt): Result<Receipt> {
        return try {
            // Handle image file management before updating
            val finalReceipt = handleImageFileForSaving(receipt)

            receiptRepository.updateReceipt(finalReceipt)
            Result.success(finalReceipt)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun handleImageFileForSaving(receipt: Receipt): Receipt {
        val imagePath = receipt.imagePath

        // If no image or no image manager, return as is
        if (imagePath == null || imageFileManager == null) {
            return receipt
        }

        // Check if this is a temporary file that needs to be moved
        if (imagePath.contains("temp_scan_")) {
            val finalPath = imageFileManager.moveToFinalLocation(imagePath, receipt.id)
            if (finalPath != null) {
                return receipt.copy(imagePath = finalPath)
            } else {
                println("UpdateReceiptUseCase: Failed to move file, keeping original path")
                return receipt
            }
        } else {
            println("UpdateReceiptUseCase: Image path is not temporary, keeping as is: $imagePath")
            return receipt
        }
    }
}


