package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.domain.service.ImageFileManager

/**
 * Use case for saving a receipt with its products
 * Handles image file management during save operation
 */
class SaveReceiptUseCase(
    private val receiptRepository: ReceiptRepository,
    private val imageFileManager: ImageFileManager? = null
) {
    suspend operator fun invoke(receipt: Receipt): Result<Receipt> {
        return try {
            // Handle image file management before saving
            val finalReceipt = handleImageFileForSaving(receipt)

            receiptRepository.saveReceipt(finalReceipt)
            Result.success(finalReceipt)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun handleImageFileForSaving(receipt: Receipt): Receipt {
        val imagePath = receipt.imagePath

        // If no image or no image manager, return as is
        if (imagePath == null || imageFileManager == null) {
            return receipt
        }

        // Check if this is a temporary file that needs to be moved
        if (imagePath.contains("temp_scan_")) {
            val finalPath = imageFileManager.moveToFinalLocation(imagePath, receipt.id)
            if (finalPath != null) {
                return receipt.copy(imagePath = finalPath)
            } else {
                println("SaveReceiptUseCase: Failed to move file, keeping original path")
                return receipt
            }
        } else {
            println("SaveReceiptUseCase: Image path is not temporary, keeping as is: $imagePath")
            return receipt
        }
    }
}


