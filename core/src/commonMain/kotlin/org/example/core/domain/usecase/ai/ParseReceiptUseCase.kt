package org.example.core.domain.usecase.ai

import kotlinx.coroutines.flow.first
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.model.ai.ParsedReceiptData
import org.example.core.domain.repository.AIRepository
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase

class ParseReceiptUseCase(
    private val aiRepository: AIRepository,
    private val getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
) {
    suspend operator fun invoke(ocrText: String): Result<ParsedReceiptData> {
        return try {
            println("ParseReceiptUseCase: Starting with OCR text length: ${ocrText.length}")

            val categories = getTypesAndCategoriesUseCase.getCategories().first().map { it.name }
            val types = getTypesAndCategoriesUseCase.getTypes().first().map { it.name }

            println("ParseReceiptUseCase: Got ${categories.size} categories and ${types.size} types")
            println("ParseReceiptUseCase: Calling aiRepository.parseReceiptText...")

            val result = aiRepository.parseReceiptText(ocrText, categories, types)

            if (result != null) {
                println("ParseReceiptUseCase: SUCCESS - got parsed data: ${result.storeName}")
                Result.success(result)
            } else {
                println("ParseReceiptUseCase: FAILURE - aiRepository returned null")
                Result.failure(ParseReceiptException("Failed to parse receipt"))
            }
        } catch (e: Exception) {
            println("ParseReceiptUseCase: EXCEPTION - ${e.message}")
            Result.failure(e)
        }
    }
}