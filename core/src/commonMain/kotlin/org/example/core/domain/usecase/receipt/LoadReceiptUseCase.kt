package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt

class ReceiptNotFoundException(message: String) : Exception(message)
class ReceiptLoadException(message: String, cause: Throwable? = null) : Exception(message, cause)

class LoadReceiptUseCase(
    private val getReceiptsUseCase: GetReceiptsUseCase
) {
    suspend fun loadReceipt(receiptId: String, mode: ScreenMode): Result<ReceiptEditData> {
        return try {
            val receipt = getReceiptsUseCase.getReceiptById(receiptId)
            if (receipt != null) {
                Result.success(ReceiptEditData(receipt, mode))
            } else {
                Result.failure(ReceiptNotFoundException("Nie znaleziono paragonu o ID: $receiptId"))
            }
        } catch (e: Exception) {
            Result.failure(ReceiptLoadException("Wystąpił błąd podczas ładowania: ${e.message}", e))
        }
    }
}

data class ReceiptEditData(
    val receipt: Receipt,
    val mode: ScreenMode
)

enum class ScreenMode {
    /*NEW,*/ EDIT, DUPLICATE
}
