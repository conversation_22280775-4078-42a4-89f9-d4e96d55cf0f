package org.example.core.domain.service

/**
 * Platform-agnostic interface for managing receipt image files
 * This service handles the lifecycle of receipt images including temporary files,
 * final storage, and cleanup operations.
 */
interface ImageFileManager {
    /**
     * Moves temporary file to final location with receipt ID
     * @param tempFilePath Path to temporary file
     * @param receiptId Receipt ID for final filename
     * @return Final file path or null if failed
     */
    fun moveToFinalLocation(tempFilePath: String, receiptId: String): String?
    
    /**
     * Deletes a temporary file
     * @param tempFilePath Path to temporary file
     * @return true if deleted successfully
     */
    fun deleteTempFile(tempFilePath: String): Boolean
    
    /**
     * Deletes a receipt image file
     * @param receiptImagePath Path to receipt image file
     * @return true if deleted successfully
     */
    fun deleteReceiptFile(receiptImagePath: String): Boolean
    
    /**
     * Cleans up old temporary files
     * Should be called periodically to prevent storage bloat
     */
    fun cleanupOldTempFiles()
}
