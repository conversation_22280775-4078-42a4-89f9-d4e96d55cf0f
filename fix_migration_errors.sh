#!/bin/bash

# Script to fix migration errors systematically

echo "🔧 Fixing migration errors..."

# Fix AddReceiptViewModel errors
echo "📝 Fixing AddReceiptViewModel..."

# Fix duplicateState references
sed -i '' 's/duplicateState = DuplicateState\./duplicate = DuplicateState./g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/AddReceiptViewModel.kt
sed -i '' 's/saveState = SaveState\./save = SaveState./g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/AddReceiptViewModel.kt
sed -i '' 's/openAiState = OpenAiState\./openAi = OpenAiState./g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/AddReceiptViewModel.kt
sed -i '' 's/isLoadingReceipt = false/loadReceipt = LoadState.Idle/g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/AddReceiptViewModel.kt

# Fix ExportManager
echo "📤 Fixing ExportManager..."
sed -i '' 's/exportState = ExportState\./export = ExportState./g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/ExportManager.kt

# Fix ScanStateReducer
echo "🔍 Fixing ScanStateReducer..."
sed -i '' 's/scanState = ScanState\./scan = ScanState./g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/ScanStateReducer.kt

# Fix OpenAiResponseMapper
echo "🤖 Fixing OpenAiResponseMapper..."
sed -i '' 's/storeName = /formData = currentState.formData.copy(storeName = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/storeAddress = /storeAddress = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/purchaseDate = /purchaseDate = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/receiptSumInCents = /receiptSumInCents = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/purchaseMethod = /purchaseMethod = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/products = /products = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt
sed -i '' 's/openAiState = /operations = currentState.operations.copy(openAi = /g' feature/addreceipt/src/commonMain/kotlin/org/example/addReceipt/mapper/OpenAiResponseMapper.kt

echo "✅ Migration errors fixed!"
